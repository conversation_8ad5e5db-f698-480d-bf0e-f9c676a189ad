// Dashboard management
class Dashboard {
  constructor() {
    this.currentPage = 'dashboard';
    this.init();
  }

  init() {
    this.setupNavigation();
    this.setupClock();
    this.loadAdminInfo();
    this.loadDashboardData();
  }

  // Setup navigation
  setupNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    const pages = document.querySelectorAll('.page');

    navItems.forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        const page = item.dataset.page;
        this.switchPage(page);
      });
    });
  }

  // Switch between pages
  switchPage(page) {
    // Update navigation
    document.querySelectorAll('.nav-item').forEach(item => {
      item.classList.remove('active');
    });
    document.querySelector(`[data-page="${page}"]`).classList.add('active');

    // Update pages
    document.querySelectorAll('.page').forEach(p => {
      p.classList.remove('active');
    });
    document.getElementById(`${page}-page`).classList.add('active');

    // Update page title
    const titles = {
      dashboard: 'Dashboard',
      categories: 'Quản lý Danh mục',
      products: 'Quản lý Sản phẩm',
      contacts: 'Quản lý Liên hệ',
      settings: 'Cài đặt'
    };
    document.getElementById('pageTitle').textContent = titles[page];

    this.currentPage = page;

    // Load page-specific data
    this.loadPageData(page);
  }

  // Load page-specific data
  async loadPageData(page) {
    switch (page) {
      case 'dashboard':
        await this.loadDashboardData();
        break;
      case 'categories':
        await this.loadCategories();
        break;
      case 'products':
        await this.loadProducts();
        break;
      case 'contacts':
        await this.loadContacts();
        break;
      case 'settings':
        await this.loadSettings();
        break;
    }
  }

  // Setup clock
  setupClock() {
    const updateClock = () => {
      const now = new Date();
      document.getElementById('currentTime').textContent = now.toLocaleString('vi-VN');
    };
    updateClock();
    setInterval(updateClock, 1000);
  }

  // Load admin info
  loadAdminInfo() {
    const admin = auth.getCurrentAdmin();
    if (admin) {
      document.getElementById('adminName').textContent = admin.username;
      document.getElementById('adminRole').textContent = admin.role === 'super_admin' ? 'Super Admin' : 'Admin';
      
      // Settings page
      if (document.getElementById('settingsUsername')) {
        document.getElementById('settingsUsername').textContent = admin.username;
        document.getElementById('settingsEmail').textContent = admin.email;
        document.getElementById('settingsRole').textContent = admin.role === 'super_admin' ? 'Super Admin' : 'Admin';
      }
    }
  }

  // Load dashboard data
  async loadDashboardData() {
    try {
      utils.showLoading();
      const data = await api.getDashboardData();
      
      if (data && data.status === 'success') {
        this.updateDashboardStats(data.data);
      }
    } catch (error) {
      console.error('Error loading dashboard:', error);
      utils.showNotification('Lỗi tải dữ liệu dashboard', 'error');
    } finally {
      utils.hideLoading();
    }
  }

  // Update dashboard statistics
  updateDashboardStats(data) {
    const { overview, recentContacts, lowStockProducts } = data;

    // Update stats cards
    document.getElementById('categoriesCount').textContent = overview.categories.total;
    document.getElementById('productsCount').textContent = overview.products.total;
    document.getElementById('contactsCount').textContent = overview.contacts.total;
    document.getElementById('totalValue').textContent = utils.formatCurrency(overview.products.totalValue || 0);

    // Update recent contacts
    const recentContactsDiv = document.getElementById('recentContacts');
    if (recentContacts && recentContacts.length > 0) {
      recentContactsDiv.innerHTML = recentContacts.map(contact => `
        <div class="recent-item">
          <strong>${contact.name}</strong> - ${contact.email}<br>
          <small>${utils.truncateText(contact.message, 60)}</small><br>
          <small class="text-muted">${utils.formatRelativeTime(contact.createdAt)}</small>
        </div>
      `).join('');
    } else {
      recentContactsDiv.innerHTML = '<p>Không có liên hệ nào</p>';
    }

    // Update low stock products
    const lowStockDiv = document.getElementById('lowStockProducts');
    if (lowStockProducts && lowStockProducts.length > 0) {
      lowStockDiv.innerHTML = lowStockProducts.map(product => `
        <div class="recent-item">
          <strong>${product.name}</strong><br>
          <small>Tồn kho: ${product.stockQuantity}</small><br>
          <small class="text-muted">Danh mục: ${product.category.name}</small>
        </div>
      `).join('');
    } else {
      lowStockDiv.innerHTML = '<p>Tất cả sản phẩm đều đủ hàng</p>';
    }
  }

  // Load categories
  async loadCategories() {
    try {
      const data = await api.getCategories();
      
      if (data && data.status === 'success') {
        this.renderCategoriesTable(data.data.categories);
      }
    } catch (error) {
      console.error('Error loading categories:', error);
      utils.showNotification('Lỗi tải danh mục', 'error');
    }
  }

  // Render categories table
  renderCategoriesTable(categories) {
    const tbody = document.querySelector('#categoriesTable tbody');
    
    if (categories.length === 0) {
      tbody.innerHTML = '<tr><td colspan="5" class="loading">Không có danh mục nào</td></tr>';
      return;
    }

    tbody.innerHTML = categories.map(category => `
      <tr>
        <td>${category.name}</td>
        <td>${utils.truncateText(category.description || '', 50)}</td>
        <td>${utils.getStatusBadge(category.isActive)}</td>
        <td>${utils.formatDate(category.createdAt)}</td>
        <td>
          <button class="btn-sm btn-edit" onclick="dashboard.editCategory('${category._id}')">Sửa</button>
          <button class="btn-sm btn-delete" onclick="dashboard.deleteCategory('${category._id}')">Xóa</button>
        </td>
      </tr>
    `).join('');
  }

  // Load products
  async loadProducts() {
    try {
      const data = await api.getProducts();
      
      if (data && data.status === 'success') {
        this.renderProductsTable(data.data.products);
      }
    } catch (error) {
      console.error('Error loading products:', error);
      utils.showNotification('Lỗi tải sản phẩm', 'error');
    }
  }

  // Render products table
  renderProductsTable(products) {
    const tbody = document.querySelector('#productsTable tbody');
    
    if (products.length === 0) {
      tbody.innerHTML = '<tr><td colspan="7" class="loading">Không có sản phẩm nào</td></tr>';
      return;
    }

    tbody.innerHTML = products.map(product => `
      <tr>
        <td><img src="${product.imageUrl}" alt="${product.name}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 5px;"></td>
        <td>${product.name}</td>
        <td>${product.category ? product.category.name : 'N/A'}</td>
        <td>${utils.formatCurrency(product.price)}</td>
        <td>${product.stockQuantity}</td>
        <td>${utils.getStatusBadge(product.isActive)}</td>
        <td>
          <button class="btn-sm btn-edit" onclick="dashboard.editProduct('${product._id}')">Sửa</button>
          <button class="btn-sm btn-delete" onclick="dashboard.deleteProduct('${product._id}')">Xóa</button>
        </td>
      </tr>
    `).join('');
  }

  // Load contacts
  async loadContacts() {
    try {
      const data = await api.getContacts();
      
      if (data && data.status === 'success') {
        this.renderContactsTable(data.data.contacts);
      }
    } catch (error) {
      console.error('Error loading contacts:', error);
      utils.showNotification('Lỗi tải liên hệ', 'error');
    }
  }

  // Render contacts table
  renderContactsTable(contacts) {
    const tbody = document.querySelector('#contactsTable tbody');
    
    if (contacts.length === 0) {
      tbody.innerHTML = '<tr><td colspan="6" class="loading">Không có liên hệ nào</td></tr>';
      return;
    }

    tbody.innerHTML = contacts.map(contact => `
      <tr>
        <td>${contact.name}</td>
        <td>${contact.email}</td>
        <td>${utils.truncateText(contact.message, 60)}</td>
        <td>${utils.getStatusBadge(contact.status, 'contact')}</td>
        <td>${utils.formatDate(contact.createdAt)}</td>
        <td>
          <button class="btn-sm btn-view" onclick="dashboard.viewContact('${contact._id}')">Xem</button>
          <button class="btn-sm btn-delete" onclick="dashboard.deleteContact('${contact._id}')">Xóa</button>
        </td>
      </tr>
    `).join('');
  }

  // Load settings
  async loadSettings() {
    // Settings are already loaded in loadAdminInfo
    // You can add more settings-related data loading here
  }

  // Category actions
  async editCategory(id) {
    utils.showNotification('Chức năng chỉnh sửa danh mục sẽ được phát triển', 'info');
  }

  async deleteCategory(id) {
    if (utils.confirm('Bạn có chắc chắn muốn xóa danh mục này?')) {
      try {
        await api.deleteCategory(id);
        utils.showNotification('Xóa danh mục thành công', 'success');
        this.loadCategories();
      } catch (error) {
        utils.showNotification('Lỗi xóa danh mục', 'error');
      }
    }
  }

  // Product actions
  async editProduct(id) {
    utils.showNotification('Chức năng chỉnh sửa sản phẩm sẽ được phát triển', 'info');
  }

  async deleteProduct(id) {
    if (utils.confirm('Bạn có chắc chắn muốn xóa sản phẩm này?')) {
      try {
        await api.deleteProduct(id);
        utils.showNotification('Xóa sản phẩm thành công', 'success');
        this.loadProducts();
      } catch (error) {
        utils.showNotification('Lỗi xóa sản phẩm', 'error');
      }
    }
  }

  // Contact actions
  async viewContact(id) {
    utils.showNotification('Chức năng xem chi tiết liên hệ sẽ được phát triển', 'info');
  }

  async deleteContact(id) {
    if (utils.confirm('Bạn có chắc chắn muốn xóa liên hệ này?')) {
      try {
        await api.deleteContact(id);
        utils.showNotification('Xóa liên hệ thành công', 'success');
        this.loadContacts();
      } catch (error) {
        utils.showNotification('Lỗi xóa liên hệ', 'error');
      }
    }
  }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  if (auth.requireAuth()) {
    window.dashboard = new Dashboard();
  }
});
