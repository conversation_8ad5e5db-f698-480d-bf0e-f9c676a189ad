// API management class
class ApiManager {
  constructor() {
    this.baseUrl = 'http://localhost:5001/api';
  }

  // Generic API request method
  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...auth.getAuthHeaders(),
        ...options.headers
      },
      ...options
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        if (response.status === 401) {
          auth.logout();
          return null;
        }
        throw new Error(data.message || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // Dashboard API
  async getDashboardData() {
    return await this.request('/admin/dashboard');
  }

  async getAnalytics(period = 30) {
    return await this.request(`/admin/dashboard/analytics?period=${period}`);
  }

  // Categories API
  async getCategories(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return await this.request(`/admin/categories?${queryString}`);
  }

  async createCategory(categoryData) {
    return await this.request('/admin/categories', {
      method: 'POST',
      body: JSON.stringify(categoryData)
    });
  }

  async updateCategory(id, categoryData) {
    return await this.request(`/admin/categories/${id}`, {
      method: 'PUT',
      body: JSON.stringify(categoryData)
    });
  }

  async deleteCategory(id) {
    return await this.request(`/admin/categories/${id}`, {
      method: 'DELETE'
    });
  }

  // Products API
  async getProducts(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return await this.request(`/admin/products?${queryString}`);
  }

  async createProduct(productData) {
    return await this.request('/admin/products', {
      method: 'POST',
      body: JSON.stringify(productData)
    });
  }

  async updateProduct(id, productData) {
    return await this.request(`/admin/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(productData)
    });
  }

  async deleteProduct(id) {
    return await this.request(`/admin/products/${id}`, {
      method: 'DELETE'
    });
  }

  // Contacts API
  async getContacts(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return await this.request(`/admin/contacts?${queryString}`);
  }

  async getContact(id) {
    return await this.request(`/admin/contacts/${id}`);
  }

  async updateContact(id, contactData) {
    return await this.request(`/admin/contacts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(contactData)
    });
  }

  async deleteContact(id) {
    return await this.request(`/admin/contacts/${id}`, {
      method: 'DELETE'
    });
  }

  async bulkUpdateContacts(contactIds, updateData) {
    return await this.request('/admin/contacts/bulk-update', {
      method: 'POST',
      body: JSON.stringify({ contactIds, ...updateData })
    });
  }
}

// Global API instance
const api = new ApiManager();

// Utility functions
const utils = {
  // Format currency
  formatCurrency(amount) {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  },

  // Format date
  formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  },

  // Format relative time
  formatRelativeTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'Vừa xong';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} phút trước`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} giờ trước`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} ngày trước`;
    
    return this.formatDate(dateString);
  },

  // Truncate text
  truncateText(text, maxLength = 50) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  },

  // Show loading overlay
  showLoading() {
    document.getElementById('loadingOverlay').style.display = 'flex';
  },

  // Hide loading overlay
  hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
  },

  // Show notification (simple alert for now)
  showNotification(message, type = 'info') {
    // You can implement a more sophisticated notification system here
    if (type === 'error') {
      alert('Lỗi: ' + message);
    } else if (type === 'success') {
      alert('Thành công: ' + message);
    } else {
      alert(message);
    }
  },

  // Confirm dialog
  confirm(message) {
    return confirm(message);
  },

  // Get status badge HTML
  getStatusBadge(status, type = 'general') {
    const badges = {
      general: {
        true: '<span class="status-badge status-active">Hoạt động</span>',
        false: '<span class="status-badge status-inactive">Không hoạt động</span>',
        active: '<span class="status-badge status-active">Hoạt động</span>',
        inactive: '<span class="status-badge status-inactive">Không hoạt động</span>'
      },
      contact: {
        new: '<span class="status-badge status-new">Mới</span>',
        read: '<span class="status-badge status-read">Đã đọc</span>',
        replied: '<span class="status-badge status-active">Đã trả lời</span>',
        resolved: '<span class="status-badge status-active">Đã giải quyết</span>'
      }
    };

    return badges[type][status] || status;
  },

  // Debounce function
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
};
