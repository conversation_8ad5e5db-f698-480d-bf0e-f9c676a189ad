// Authentication management
class AuthManager {
  constructor() {
    this.apiUrl = 'http://localhost:5001/api';
    this.token = localStorage.getItem('adminToken');
    this.admin = JSON.parse(localStorage.getItem('adminData') || 'null');
  }

  // Check if user is authenticated
  isAuthenticated() {
    return !!this.token && !!this.admin;
  }

  // Get auth headers for API requests
  getAuthHeaders() {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`
    };
  }

  // Login
  async login(username, password) {
    try {
      const response = await fetch(`${this.apiUrl}/admin/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
      });

      const data = await response.json();

      if (data.status === 'success') {
        this.token = data.data.token;
        this.admin = data.data.admin;
        
        localStorage.setItem('adminToken', this.token);
        localStorage.setItem('adminData', JSON.stringify(this.admin));
        
        return { success: true, data: data.data };
      } else {
        return { success: false, message: data.message };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, message: 'Lỗi kết nối. Vui lòng thử lại.' };
    }
  }

  // Logout
  logout() {
    this.token = null;
    this.admin = null;
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminData');
    window.location.href = 'login.html';
  }

  // Get current admin info
  getCurrentAdmin() {
    return this.admin;
  }

  // Check authentication and redirect if needed
  requireAuth() {
    if (!this.isAuthenticated()) {
      window.location.href = 'login.html';
      return false;
    }
    return true;
  }

  // Verify token with server
  async verifyToken() {
    if (!this.token) return false;

    try {
      const response = await fetch(`${this.apiUrl}/admin/auth/me`, {
        headers: this.getAuthHeaders()
      });

      if (response.ok) {
        const data = await response.json();
        this.admin = data.data.admin;
        localStorage.setItem('adminData', JSON.stringify(this.admin));
        return true;
      } else {
        this.logout();
        return false;
      }
    } catch (error) {
      console.error('Token verification error:', error);
      return false;
    }
  }
}

// Global auth instance
const auth = new AuthManager();

// Login form handler
if (document.getElementById('loginForm')) {
  document.getElementById('loginForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const errorDiv = document.getElementById('errorMessage');
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    
    // Show loading state
    submitBtn.disabled = true;
    btnText.style.display = 'none';
    btnLoading.style.display = 'inline';
    errorDiv.style.display = 'none';
    
    try {
      const result = await auth.login(username, password);
      
      if (result.success) {
        window.location.href = 'index.html';
      } else {
        errorDiv.textContent = result.message;
        errorDiv.style.display = 'block';
      }
    } catch (error) {
      errorDiv.textContent = 'Đã xảy ra lỗi. Vui lòng thử lại.';
      errorDiv.style.display = 'block';
    } finally {
      // Reset button state
      submitBtn.disabled = false;
      btnText.style.display = 'inline';
      btnLoading.style.display = 'none';
    }
  });
}

// Auto-redirect if already logged in (on login page)
if (window.location.pathname.includes('login.html') && auth.isAuthenticated()) {
  window.location.href = 'index.html';
}

// Logout handler
if (document.getElementById('logoutBtn')) {
  document.getElementById('logoutBtn').addEventListener('click', () => {
    if (confirm('Bạn có chắc chắn muốn đăng xuất?')) {
      auth.logout();
    }
  });
}

// Check authentication on dashboard pages
if (window.location.pathname.includes('index.html') || window.location.pathname === '/') {
  if (!auth.requireAuth()) {
    // Will redirect to login if not authenticated
  } else {
    // Verify token on page load
    auth.verifyToken();
  }
}
