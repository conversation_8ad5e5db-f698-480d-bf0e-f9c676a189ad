<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CoffeeSource Admin Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h2>CoffeeSource</h2>
            <p>Admin Panel</p>
        </div>
        
        <nav class="sidebar-nav">
            <a href="#dashboard" class="nav-item active" data-page="dashboard">
                📊 Dashboard
            </a>
            <a href="#categories" class="nav-item" data-page="categories">
                📁 Danh mục
            </a>
            <a href="#products" class="nav-item" data-page="products">
                ☕ Sản phẩm
            </a>
            <a href="#contacts" class="nav-item" data-page="contacts">
                📧 Liên hệ
            </a>
            <a href="#settings" class="nav-item" data-page="settings">
                ⚙️ Cài đặt
            </a>
        </nav>
        
        <div class="sidebar-footer">
            <div class="admin-info">
                <p id="adminName">Loading...</p>
                <p id="adminRole">Loading...</p>
            </div>
            <button id="logoutBtn" class="btn-logout">Đăng xuất</button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <header class="main-header">
            <h1 id="pageTitle">Dashboard</h1>
            <div class="header-actions">
                <span id="currentTime"></span>
            </div>
        </header>

        <main class="content-area">
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📁</div>
                        <div class="stat-info">
                            <h3 id="categoriesCount">0</h3>
                            <p>Danh mục</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">☕</div>
                        <div class="stat-info">
                            <h3 id="productsCount">0</h3>
                            <p>Sản phẩm</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">📧</div>
                        <div class="stat-info">
                            <h3 id="contactsCount">0</h3>
                            <p>Liên hệ</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <div class="stat-info">
                            <h3 id="totalValue">0đ</h3>
                            <p>Tổng giá trị</p>
                        </div>
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <h3>Liên hệ gần đây</h3>
                        <div id="recentContacts" class="recent-list">
                            <p>Đang tải...</p>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3>Sản phẩm sắp hết hàng</h3>
                        <div id="lowStockProducts" class="recent-list">
                            <p>Đang tải...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Categories Page -->
            <div id="categories-page" class="page">
                <div class="page-header">
                    <h2>Quản lý Danh mục</h2>
                    <button id="addCategoryBtn" class="btn-primary">Thêm danh mục</button>
                </div>
                
                <div class="table-container">
                    <table id="categoriesTable" class="data-table">
                        <thead>
                            <tr>
                                <th>Tên danh mục</th>
                                <th>Mô tả</th>
                                <th>Trạng thái</th>
                                <th>Ngày tạo</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="5" class="loading">Đang tải dữ liệu...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Products Page -->
            <div id="products-page" class="page">
                <div class="page-header">
                    <h2>Quản lý Sản phẩm</h2>
                    <button id="addProductBtn" class="btn-primary">Thêm sản phẩm</button>
                </div>
                
                <div class="filters">
                    <select id="categoryFilter">
                        <option value="">Tất cả danh mục</option>
                    </select>
                    <select id="statusFilter">
                        <option value="">Tất cả trạng thái</option>
                        <option value="true">Hoạt động</option>
                        <option value="false">Không hoạt động</option>
                    </select>
                </div>
                
                <div class="table-container">
                    <table id="productsTable" class="data-table">
                        <thead>
                            <tr>
                                <th>Hình ảnh</th>
                                <th>Tên sản phẩm</th>
                                <th>Danh mục</th>
                                <th>Giá</th>
                                <th>Tồn kho</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="7" class="loading">Đang tải dữ liệu...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Contacts Page -->
            <div id="contacts-page" class="page">
                <div class="page-header">
                    <h2>Quản lý Liên hệ</h2>
                    <div class="contact-filters">
                        <select id="contactStatusFilter">
                            <option value="">Tất cả trạng thái</option>
                            <option value="new">Mới</option>
                            <option value="read">Đã đọc</option>
                            <option value="replied">Đã trả lời</option>
                            <option value="resolved">Đã giải quyết</option>
                        </select>
                    </div>
                </div>
                
                <div class="table-container">
                    <table id="contactsTable" class="data-table">
                        <thead>
                            <tr>
                                <th>Tên</th>
                                <th>Email</th>
                                <th>Tin nhắn</th>
                                <th>Trạng thái</th>
                                <th>Ngày gửi</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="loading">Đang tải dữ liệu...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Settings Page -->
            <div id="settings-page" class="page">
                <div class="page-header">
                    <h2>Cài đặt</h2>
                </div>
                
                <div class="settings-grid">
                    <div class="settings-card">
                        <h3>Thông tin tài khoản</h3>
                        <div class="setting-item">
                            <label>Tên đăng nhập:</label>
                            <span id="settingsUsername">Loading...</span>
                        </div>
                        <div class="setting-item">
                            <label>Email:</label>
                            <span id="settingsEmail">Loading...</span>
                        </div>
                        <div class="setting-item">
                            <label>Vai trò:</label>
                            <span id="settingsRole">Loading...</span>
                        </div>
                    </div>
                    
                    <div class="settings-card">
                        <h3>Thống kê hệ thống</h3>
                        <div id="systemStats">
                            <p>Đang tải...</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
        <p>Đang tải...</p>
    </div>

    <!-- <script src="js/auth.js"></script>
    <script src="js/api.js"></script>
    <script src="js/dashboard.js"></script> -->
</body>
</html>
