# CoffeeSource Admin Dashboard

A comprehensive admin dashboard for managing the CoffeeSource coffee eCommerce platform. Built with vanilla HTML, CSS, and JavaScript for simplicity and performance.

## Features

### 🔐 Authentication System
- Secure JWT-based authentication
- Role-based access control (Admin, Super Admin)
- Account lockout protection after failed attempts
- Session management with token verification

### 📊 Dashboard Overview
- Real-time statistics (Categories, Products, Contacts, Total Value)
- Recent contact submissions
- Low stock product alerts
- System health monitoring

### 📁 Category Management
- View all product categories
- Create, edit, and delete categories
- Status management (Active/Inactive)
- Search and filter capabilities

### ☕ Product Management
- Complete product CRUD operations
- Image preview and management
- Stock quantity tracking
- Featured product management
- Category assignment
- Price and discount management

### 📧 Contact Management
- View all customer inquiries
- Status tracking (New, Read, Replied, Resolved)
- Bulk operations
- Response management
- Contact analytics

### ⚙️ Settings & Administration
- Admin account information
- System statistics
- User role management (Super Admin only)

## Tech Stack

- **Frontend**: Vanilla HTML5, CSS3, JavaScript (ES6+)
- **Styling**: Custom CSS with CSS Grid and Flexbox
- **Authentication**: JWT tokens with localStorage
- **API Communication**: Fetch API
- **Icons**: Unicode emojis for simplicity

## Installation & Setup

1. **Install dependencies**
   ```bash
   cd admin
   npm install
   ```

2. **Start development server**
   ```bash
   npm run dev
   ```
   This will start a live server on `http://localhost:3001`

3. **Production deployment**
   ```bash
   npm start
   ```

## Default Admin Credentials

The system comes with pre-seeded admin accounts:

### Super Admin
- **Username**: `admin`
- **Password**: `admin123`
- **Permissions**: Full system access, can create other admins

### Regular Admin
- **Username**: `manager`
- **Password**: `manager123`
- **Permissions**: Standard admin operations

## File Structure

```
admin/
├── package.json              # Dependencies and scripts
├── login.html                # Login page
├── index.html                # Main dashboard
├── css/
│   └── admin.css             # All styling
├── js/
│   ├── auth.js               # Authentication management
│   ├── api.js                # API communication
│   └── dashboard.js          # Dashboard functionality
└── README.md                 # This file
```

## API Integration

The admin dashboard communicates with the backend API at `http://localhost:5001/api/admin/`

### Authentication Endpoints
- `POST /admin/auth/login` - Admin login
- `GET /admin/auth/me` - Get current admin info
- `POST /admin/auth/logout` - Logout

### Management Endpoints
- `GET /admin/dashboard` - Dashboard data
- `GET /admin/categories` - Category management
- `GET /admin/products` - Product management
- `GET /admin/contacts` - Contact management

## Security Features

### Authentication
- JWT token-based authentication
- Automatic token verification
- Secure logout with token cleanup

### Account Protection
- Failed login attempt tracking
- Account lockout after 5 failed attempts
- Rate limiting on login endpoint

### Authorization
- Role-based access control
- Protected admin routes
- Permission validation

## Usage Guide

### 1. Login
1. Navigate to `http://localhost:3001/login.html`
2. Enter admin credentials
3. Click "Đăng nhập" to access dashboard

### 2. Dashboard Navigation
- Use the sidebar to navigate between sections
- Dashboard shows real-time statistics
- Recent activities are displayed on the main page

### 3. Managing Categories
1. Click "Danh mục" in sidebar
2. View all categories in table format
3. Use "Thêm danh mục" to create new categories
4. Edit or delete existing categories

### 4. Managing Products
1. Click "Sản phẩm" in sidebar
2. View products with images and details
3. Filter by category or status
4. Manage stock levels and pricing

### 5. Managing Contacts
1. Click "Liên hệ" in sidebar
2. View customer inquiries
3. Update status and add notes
4. Track response progress

## Customization

### Styling
- Modify `css/admin.css` for visual customization
- CSS variables are defined at the top for easy theming
- Responsive design works on all screen sizes

### Functionality
- Extend `js/dashboard.js` for new features
- Add new API endpoints in `js/api.js`
- Customize authentication in `js/auth.js`

## Development

### Adding New Features
1. Create new HTML sections in `index.html`
2. Add corresponding CSS in `admin.css`
3. Implement JavaScript functionality in appropriate files
4. Update navigation and routing

### API Integration
1. Add new API methods in `js/api.js`
2. Handle responses in dashboard components
3. Implement error handling and loading states

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Performance

- Lightweight vanilla JavaScript (no frameworks)
- Efficient DOM manipulation
- Lazy loading of data
- Optimized CSS with minimal dependencies

## Security Considerations

- Always use HTTPS in production
- Implement proper CORS policies
- Regular security audits
- Keep dependencies updated
- Use environment variables for sensitive data

## Troubleshooting

### Common Issues

1. **Login fails**: Check API server is running on port 5001
2. **Data not loading**: Verify authentication token
3. **CORS errors**: Ensure API CORS is configured correctly
4. **Styling issues**: Clear browser cache

### Debug Mode
- Open browser developer tools
- Check console for error messages
- Verify network requests in Network tab
- Check localStorage for authentication data

## Contributing

1. Follow existing code style
2. Add comments for complex functionality
3. Test on multiple browsers
4. Update documentation for new features

## License

MIT License - see main project license for details.
