/* CSS Variables */
:root {
  --primary-brown: #8B4513;
  --dark-brown: #5D2F0A;
  --light-brown: #D2B48C;
  --cream: #F5F5DC;
  --warm-white: #FFF8DC;
  --text-dark: #2C1810;
  --text-light: #6B4423;
  --accent-gold: #DAA520;
  --success: #27ae60;
  --warning: #f39c12;
  --danger: #e74c3c;
  --info: #3498db;
  --sidebar-width: 250px;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background-color: #f8f9fa;
  color: var(--text-dark);
  line-height: 1.6;
}

/* Login Page Styles */
.login-page {
  background: linear-gradient(135deg, var(--dark-brown) 0%, var(--primary-brown) 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-container {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
}

.login-card {
  background: white;
  border-radius: 15px;
  padding: 2.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h1 {
  color: var(--primary-brown);
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.login-header h2 {
  color: var(--text-dark);
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.login-header p {
  color: var(--text-light);
  font-size: 0.9rem;
}

.login-form {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-dark);
}

.form-group input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-brown);
}

.btn-login {
  width: 100%;
  padding: 12px;
  background-color: var(--primary-brown);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-login:hover {
  background-color: var(--dark-brown);
}

.btn-login:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.error-message {
  background-color: #fee;
  color: var(--danger);
  padding: 10px;
  border-radius: 5px;
  margin-top: 1rem;
  text-align: center;
  font-size: 0.9rem;
}

.login-footer {
  text-align: center;
  font-size: 0.8rem;
  color: var(--text-light);
  border-top: 1px solid #eee;
  padding-top: 1rem;
}

.login-footer p {
  margin-bottom: 0.25rem;
}

/* Dashboard Layout */
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: var(--sidebar-width);
  height: 100vh;
  background: linear-gradient(180deg, var(--dark-brown) 0%, var(--primary-brown) 100%);
  color: white;
  display: flex;
  flex-direction: column;
  z-index: 1000;
}

.sidebar-header {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.sidebar-header p {
  font-size: 0.9rem;
  opacity: 0.8;
}

.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
}

.nav-item {
  display: block;
  padding: 1rem 1.5rem;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-left-color: var(--accent-gold);
}

.nav-item.active {
  background-color: rgba(255, 255, 255, 0.15);
  border-left-color: var(--accent-gold);
}

.sidebar-footer {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-info {
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.admin-info p {
  margin-bottom: 0.25rem;
}

.btn-logout {
  width: 100%;
  padding: 8px;
  background-color: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-logout:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Main Content */
.main-content {
  margin-left: var(--sidebar-width);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-header {
  background: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.main-header h1 {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-dark);
}

.header-actions {
  font-size: 0.9rem;
  color: var(--text-light);
}

.content-area {
  flex: 1;
  padding: 2rem;
}

/* Page Management */
.page {
  display: none;
}

.page.active {
  display: block;
}

/* Dashboard Styles */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--cream);
  border-radius: 10px;
}

.stat-info h3 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-brown);
  margin-bottom: 0.25rem;
}

.stat-info p {
  color: var(--text-light);
  font-size: 0.9rem;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.dashboard-card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dashboard-card h3 {
  margin-bottom: 1rem;
  color: var(--text-dark);
  font-size: 1.2rem;
}

.recent-list {
  max-height: 300px;
  overflow-y: auto;
}

.recent-item {
  padding: 0.75rem 0;
  border-bottom: 1px solid #eee;
}

.recent-item:last-child {
  border-bottom: none;
}

/* Table Styles */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.page-header h2 {
  font-size: 1.5rem;
  color: var(--text-dark);
}

.btn-primary {
  background-color: var(--primary-brown);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.btn-primary:hover {
  background-color: var(--dark-brown);
}

.filters {
  margin-bottom: 1rem;
  display: flex;
  gap: 1rem;
}

.filters select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background: white;
}

.table-container {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background-color: var(--cream);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-dark);
  border-bottom: 1px solid #ddd;
}

.data-table td {
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.data-table tr:hover {
  background-color: #f8f9fa;
}

.loading {
  text-align: center;
  color: var(--text-light);
  font-style: italic;
}

/* Action Buttons */
.btn-sm {
  padding: 5px 10px;
  font-size: 0.8rem;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  margin-right: 5px;
}

.btn-edit {
  background-color: var(--info);
  color: white;
}

.btn-delete {
  background-color: var(--danger);
  color: white;
}

.btn-view {
  background-color: var(--success);
  color: white;
}

/* Status Badges */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-active {
  background-color: #d4edda;
  color: #155724;
}

.status-inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.status-new {
  background-color: #cce5ff;
  color: #004085;
}

.status-read {
  background-color: #fff3cd;
  color: #856404;
}

/* Settings Page */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.settings-card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.settings-card h3 {
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item label {
  font-weight: 500;
  color: var(--text-dark);
}

.setting-item span {
  color: var(--text-light);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .filters {
    flex-direction: column;
  }
  
  .data-table {
    font-size: 0.9rem;
  }
  
  .data-table th,
  .data-table td {
    padding: 0.5rem;
  }
}
