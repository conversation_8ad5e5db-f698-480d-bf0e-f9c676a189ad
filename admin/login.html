<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CoffeeSource Admin - Đăng nhập</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1>CoffeeSource</h1>
                <h2>Admin Dashboard</h2>
                <p>Đ<PERSON>ng nhập để quản lý hệ thống</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">Tên đăng nhập hoặc Email</label>
                    <input type="text" id="username" name="username" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Mật khẩu</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" class="btn-login">
                    <span class="btn-text">Đăng nhập</span>
                    <span class="btn-loading" style="display: none;">Đang đăng nhập...</span>
                </button>
                
                <div id="errorMessage" class="error-message" style="display: none;"></div>
            </form>
            
            <div class="login-footer">
                <p>Demo Credentials:</p>
                <p><strong>Super Admin:</strong> admin / admin123</p>
                <p><strong>Admin:</strong> manager / manager123</p>
            </div>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script>
        // Auto-fill demo credentials for testing
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const demo = urlParams.get('demo');
            
            if (demo === 'admin') {
                document.getElementById('username').value = 'admin';
                document.getElementById('password').value = 'admin123';
            } else if (demo === 'manager') {
                document.getElementById('username').value = 'manager';
                document.getElementById('password').value = 'manager123';
            }
        });
    </script>
</body>
</html>
