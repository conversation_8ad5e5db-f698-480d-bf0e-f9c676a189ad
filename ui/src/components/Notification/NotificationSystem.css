/* Notification Container */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 400px;
  pointer-events: none;
}

/* Individual Notification */
.notification {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: flex-start;
  gap: 12px;
  min-width: 300px;
  max-width: 400px;
  pointer-events: auto;
  transform: translateX(100%);
  animation: slideIn 0.3s ease forwards;
  border-left: 4px solid;
  position: relative;
  overflow: hidden;
}

/* Slide in animation */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Slide out animation */
.notification.removing {
  animation: slideOut 0.3s ease forwards;
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Notification Types */
.notification-success {
  border-left-color: #27ae60;
  background: linear-gradient(135deg, #ffffff 0%, #f8fff8 100%);
}

.notification-error {
  border-left-color: #e74c3c;
  background: linear-gradient(135deg, #ffffff 0%, #fff8f8 100%);
}

.notification-warning {
  border-left-color: #f39c12;
  background: linear-gradient(135deg, #ffffff 0%, #fffcf8 100%);
}

.notification-info {
  border-left-color: #3498db;
  background: linear-gradient(135deg, #ffffff 0%, #f8fcff 100%);
}

.notification-cart {
  border-left-color: var(--primary-brown);
  background: linear-gradient(135deg, #ffffff 0%, var(--warm-white) 100%);
}

/* Notification Icon */
.notification-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 2px;
}

/* Notification Content */
.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-dark);
  margin-bottom: 4px;
  line-height: 1.3;
}

.notification-message {
  font-size: 0.9rem;
  color: var(--text-light);
  line-height: 1.4;
  word-wrap: break-word;
}

/* Close Button */
.notification-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: var(--text-light);
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.2s ease;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--text-dark);
}

/* Progress Bar (for timed notifications) */
.notification::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: linear-gradient(90deg, 
    var(--primary-brown) 0%, 
    var(--accent-gold) 50%, 
    var(--primary-brown) 100%);
  animation: progress 4s linear forwards;
}

.notification-success::after {
  background: #27ae60;
}

.notification-error::after {
  background: #e74c3c;
}

.notification-warning::after {
  background: #f39c12;
}

.notification-info::after {
  background: #3498db;
}

@keyframes progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Hover to pause */
.notification:hover::after {
  animation-play-state: paused;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .notification {
    min-width: auto;
    max-width: none;
    margin: 0;
  }
  
  .notification-title {
    font-size: 0.9rem;
  }
  
  .notification-message {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .notification {
    padding: 12px;
    gap: 8px;
  }
  
  .notification-icon {
    font-size: 1.3rem;
  }
  
  .notification-close {
    width: 20px;
    height: 20px;
    font-size: 1rem;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .notification {
    border: 2px solid;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  }
  
  .notification-success {
    border-color: #27ae60;
  }
  
  .notification-error {
    border-color: #e74c3c;
  }
  
  .notification-warning {
    border-color: #f39c12;
  }
  
  .notification-info {
    border-color: #3498db;
  }
  
  .notification-cart {
    border-color: var(--primary-brown);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .notification {
    animation: none;
    transform: translateX(0);
  }
  
  .notification::after {
    animation: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .notification {
    background: #2c2c2c;
    color: #ffffff;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
  
  .notification-title {
    color: #ffffff;
  }
  
  .notification-message {
    color: #cccccc;
  }
  
  .notification-close {
    color: #cccccc;
  }
  
  .notification-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
  }
}

/* Focus styles for accessibility */
.notification-close:focus {
  outline: 2px solid var(--accent-gold);
  outline-offset: 2px;
}

/* Stack multiple notifications */
.notification-container .notification:nth-child(n+4) {
  opacity: 0.8;
  transform: scale(0.95) translateY(-10px);
}

.notification-container .notification:nth-child(n+6) {
  display: none;
}
