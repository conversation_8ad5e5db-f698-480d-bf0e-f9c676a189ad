import React, { useState } from 'react';
import { useCart } from '../../context/CartContext';
import { useNotification } from '../Notification/NotificationSystem';
import './CheckoutModal.css';

const CheckoutModal = ({ isOpen, onClose }) => {
  const { items, formattedTotalPrice, clearCart } = useCart();
  const { showSuccess, showError } = useNotification();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    notes: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // For demo purposes, just show success message
      showSuccess('Đơn hàng của bạn đã được gửi thành công! Chúng tôi sẽ liên hệ với bạn sớm nhất.');
      
      // Clear cart and close modal
      clearCart();
      onClose();
      
      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        address: '',
        notes: ''
      });
      
    } catch (error) {
      showError('Có lỗi xảy ra khi gửi đơn hàng. Vui lòng thử lại.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatPrice = (price) => {
    const numericPrice = parseFloat(price.replace(/[,\.]/g, ''));
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(numericPrice);
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div className="checkout-overlay" onClick={onClose} />
      
      {/* Modal */}
      <div className="checkout-modal">
        <div className="checkout-header">
          <h2>Thanh toán đơn hàng</h2>
          <button className="checkout-close" onClick={onClose}>✕</button>
        </div>

        <div className="checkout-content">
          {/* Order Summary */}
          <div className="order-summary">
            <h3>Đơn hàng của bạn</h3>
            <div className="order-items">
              {items.map(item => (
                <div key={item.id} className="order-item">
                  <img src={item.image} alt={item.name} />
                  <div className="order-item-details">
                    <h4>{item.name}</h4>
                    <p>Số lượng: {item.quantity}</p>
                    <p className="order-item-price">{formatPrice(item.price)}</p>
                  </div>
                </div>
              ))}
            </div>
            <div className="order-total">
              <strong>Tổng cộng: {formattedTotalPrice}</strong>
            </div>
          </div>

          {/* Checkout Form */}
          <form className="checkout-form" onSubmit={handleSubmit}>
            <h3>Thông tin giao hàng</h3>
            
            <div className="form-group">
              <label htmlFor="name">Họ và tên *</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">Email *</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="phone">Số điện thoại *</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="address">Địa chỉ giao hàng *</label>
              <textarea
                id="address"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                rows="3"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="notes">Ghi chú (tùy chọn)</label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows="2"
                placeholder="Ghi chú thêm về đơn hàng..."
              />
            </div>

            <div className="checkout-actions">
              <button 
                type="button" 
                className="btn-cancel"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Hủy
              </button>
              <button 
                type="submit" 
                className="btn-submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Đang xử lý...' : 'Đặt hàng'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default CheckoutModal;
