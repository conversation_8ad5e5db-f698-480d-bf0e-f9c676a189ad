/* Checkout Overlay */
.checkout-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9998;
  backdrop-filter: blur(4px);
}

/* Checkout Modal */
.checkout-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  background: white;
  border-radius: 15px;
  z-index: 9999;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* Checkout Header */
.checkout-header {
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, var(--primary-brown) 0%, var(--dark-brown) 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.checkout-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.checkout-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.checkout-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Checkout Content */
.checkout-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
  max-height: calc(90vh - 100px);
  overflow-y: auto;
}

/* Order Summary */
.order-summary {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 10px;
  height: fit-content;
}

.order-summary h3 {
  margin: 0 0 1rem 0;
  color: var(--text-dark);
  font-size: 1.2rem;
}

.order-items {
  margin-bottom: 1.5rem;
}

.order-item {
  display: flex;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid #eee;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
}

.order-item-details {
  flex: 1;
}

.order-item-details h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: var(--text-dark);
}

.order-item-details p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: var(--text-light);
}

.order-item-price {
  font-weight: 600;
  color: var(--primary-brown) !important;
}

.order-total {
  padding: 1rem 0;
  border-top: 2px solid var(--primary-brown);
  font-size: 1.2rem;
  color: var(--text-dark);
}

/* Checkout Form */
.checkout-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.checkout-form h3 {
  margin: 0;
  color: var(--text-dark);
  font-size: 1.2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: var(--text-dark);
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-brown);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* Checkout Actions */
.checkout-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.btn-cancel,
.btn-submit {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel {
  background-color: #f8f9fa;
  color: var(--text-light);
  border: 2px solid #e0e0e0;
}

.btn-cancel:hover {
  background-color: #e9ecef;
  color: var(--text-dark);
}

.btn-submit {
  background-color: var(--primary-brown);
  color: white;
}

.btn-submit:hover {
  background-color: var(--dark-brown);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
}

.btn-submit:disabled,
.btn-cancel:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Loading State */
.btn-submit:disabled {
  background-color: var(--accent-gold);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .checkout-modal {
    width: 95%;
    max-height: 95vh;
  }
  
  .checkout-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
  }
  
  .checkout-header {
    padding: 1rem 1.5rem;
  }
  
  .checkout-header h2 {
    font-size: 1.3rem;
  }
  
  .order-summary {
    padding: 1rem;
  }
  
  .checkout-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .checkout-modal {
    width: 100%;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
    top: 0;
    left: 0;
    transform: none;
  }
  
  .checkout-content {
    padding: 1rem;
    gap: 1rem;
  }
  
  .checkout-header {
    padding: 1rem;
  }
  
  .order-item {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .order-item img {
    width: 100%;
    height: 120px;
  }
}

/* Scrollbar Styling */
.checkout-content::-webkit-scrollbar {
  width: 6px;
}

.checkout-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.checkout-content::-webkit-scrollbar-thumb {
  background: var(--primary-brown);
  border-radius: 3px;
}

.checkout-content::-webkit-scrollbar-thumb:hover {
  background: var(--dark-brown);
}

/* Focus Styles */
.form-group input:focus,
.form-group textarea:focus,
.btn-cancel:focus,
.btn-submit:focus,
.checkout-close:focus {
  outline: 2px solid var(--accent-gold);
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .checkout-modal {
    border: 3px solid var(--text-dark);
  }
  
  .form-group input,
  .form-group textarea {
    border-width: 3px;
  }
  
  .btn-cancel,
  .btn-submit {
    border: 2px solid currentColor;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .checkout-modal {
    transition: none;
  }
  
  .btn-submit,
  .btn-cancel,
  .checkout-close {
    transition: none;
  }
  
  .btn-submit:disabled {
    animation: none;
  }
}
