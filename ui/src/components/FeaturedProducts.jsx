import React from 'react';
import AddToCartButton from './Cart/AddToCartButton';
import './FeaturedProducts.css';

const FeaturedProducts = () => {
  const products = [
    {
      id: 1,
      name: 'Arabica Đà Lạ<PERSON>',
      description: '<PERSON><PERSON><PERSON> cà phê Arabica cao cấp từ Đà Lạ<PERSON>',
      price: '250,000',
      originalPrice: '300,000',
      image: 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
      badge: '<PERSON><PERSON> chạy'
    },
    {
      id: 2,
      name: '<PERSON><PERSON><PERSON>hu<PERSON>',
      description: 'C<PERSON> phê Robusta đậm đà từ <PERSON>ô<PERSON>',
      price: '180,000',
      originalPrice: null,
      image: 'https://images.unsplash.com/photo-1514432324607-a09d9b4aefdd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
      badge: null
    },
    {
      id: 3,
      name: 'Máy xay cà phê',
      description: 'Máy xay cà phê chuyên nghiệp cho quán',
      price: '2,500,000',
      originalPrice: '3,000,000',
      image: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
      badge: 'Giảm giá'
    },
    {
      id: 4,
      name: 'Bộ dụng cụ pha chế',
      description: 'Bộ dụng cụ pha chế cà phê hoàn chỉnh',
      price: '850,000',
      originalPrice: null,
      image: 'https://images.unsplash.com/photo-1442512595331-e89e73853f31?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
      badge: 'Mới'
    }
  ];

  return (
    <section className="featured-products section" id="products">
      <div className="container">
        <h2 className="section-title">Sản phẩm nổi bật</h2>
        <div className="products-grid">
          {products.map(product => (
            <div key={product.id} className="product-card card">
              {product.badge && (
                <div className={`product-badge ${product.badge === 'Bán chạy' ? 'bestseller' : product.badge === 'Giảm giá' ? 'sale' : 'new'}`}>
                  {product.badge}
                </div>
              )}
              <div className="product-image">
                <img src={product.image} alt={product.name} />
                <div className="product-overlay">
                  <button className="product-action-btn">
                    ❤️
                  </button>
                  <AddToCartButton
                    product={product}
                    variant="icon"
                  />
                </div>
              </div>
              <div className="product-content">
                <h3 className="product-name">{product.name}</h3>
                <p className="product-description">{product.description}</p>
                <div className="product-price">
                  <span className="current-price">{product.price}đ</span>
                  {product.originalPrice && (
                    <span className="original-price">{product.originalPrice}đ</span>
                  )}
                </div>
                <AddToCartButton
                  product={product}
                  showQuantity={true}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
