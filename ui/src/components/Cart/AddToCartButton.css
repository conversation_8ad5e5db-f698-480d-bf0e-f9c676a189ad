/* Add to Cart Container */
.add-to-cart-container {
  position: relative;
}

/* Primary Add to <PERSON><PERSON> */
.add-to-cart-btn {
  width: 100%;
  padding: 12px 20px;
  background-color: var(--primary-brown);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.add-to-cart-btn:hover {
  background-color: var(--dark-brown);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
}

.add-to-cart-btn:active {
  transform: translateY(0);
}

.add-to-cart-btn:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

/* Button States */
.add-to-cart-btn.adding {
  background-color: var(--accent-gold);
  animation: pulse 1s infinite;
}

.add-to-cart-btn.success {
  background-color: #27ae60;
  animation: successPulse 0.6s ease;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes successPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.btn-text {
  transition: opacity 0.3s ease;
}

.btn-icon {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.add-to-cart-btn:hover .btn-icon {
  transform: scale(1.2);
}

/* Icon Variant */
.add-to-cart-icon {
  width: 45px;
  height: 45px;
  background-color: rgba(255, 255, 255, 0.9);
  color: var(--primary-brown);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.add-to-cart-icon:hover {
  background-color: var(--primary-brown);
  color: white;
  transform: scale(1.1);
}

.add-to-cart-icon.adding {
  background-color: var(--accent-gold);
  color: white;
  animation: spin 1s linear infinite;
}

.add-to-cart-icon.success {
  background-color: #27ae60;
  color: white;
  animation: bounce 0.6s ease;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.3); }
}

/* Small Variant */
.add-to-cart-small {
  width: 30px;
  height: 30px;
  background-color: var(--primary-brown);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-to-cart-small:hover {
  background-color: var(--dark-brown);
  transform: scale(1.1);
}

.add-to-cart-small.adding {
  background-color: var(--accent-gold);
  animation: pulse 1s infinite;
}

.add-to-cart-small.success {
  background-color: #27ae60;
  animation: successPulse 0.6s ease;
}

/* Quantity Selector */
.quantity-selector {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 25px;
  padding: 0.25rem;
  gap: 0.5rem;
  border: 2px solid var(--primary-brown);
}

.quantity-btn {
  width: 35px;
  height: 35px;
  border: none;
  background-color: var(--primary-brown);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.quantity-btn:hover {
  background-color: var(--dark-brown);
  transform: scale(1.1);
}

.quantity-btn:active {
  transform: scale(0.95);
}

.quantity-btn.decrease:hover {
  background-color: #e74c3c;
}

.quantity-btn.increase:hover {
  background-color: #27ae60;
}

.quantity-display {
  min-width: 40px;
  text-align: center;
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--text-dark);
  background-color: white;
  padding: 0.5rem;
  border-radius: 15px;
}

/* Loading Animation */
@keyframes addToCartLoading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.add-to-cart-btn.adding::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: addToCartLoading 1.5s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .add-to-cart-btn {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
  
  .add-to-cart-icon {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
  }
  
  .quantity-btn {
    width: 30px;
    height: 30px;
    font-size: 1rem;
  }
  
  .quantity-display {
    min-width: 35px;
    font-size: 1rem;
    padding: 0.4rem;
  }
}

@media (max-width: 480px) {
  .add-to-cart-btn {
    padding: 8px 12px;
    font-size: 0.8rem;
  }
  
  .quantity-selector {
    padding: 0.2rem;
    gap: 0.3rem;
  }
  
  .quantity-btn {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }
  
  .quantity-display {
    min-width: 30px;
    font-size: 0.9rem;
    padding: 0.3rem;
  }
}

/* Accessibility */
.add-to-cart-btn:focus,
.add-to-cart-icon:focus,
.add-to-cart-small:focus,
.quantity-btn:focus {
  outline: 2px solid var(--accent-gold);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .add-to-cart-btn,
  .add-to-cart-icon,
  .add-to-cart-small,
  .quantity-btn {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .add-to-cart-btn,
  .add-to-cart-icon,
  .add-to-cart-small,
  .quantity-btn {
    transition: none;
  }
  
  .add-to-cart-btn.adding,
  .add-to-cart-icon.adding,
  .add-to-cart-small.adding {
    animation: none;
  }
}
