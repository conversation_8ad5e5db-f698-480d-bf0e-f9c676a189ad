/* Cart Overlay */
.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.cart-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Cart Sidebar */
.cart-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background-color: white;
  z-index: 1000;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

.cart-sidebar.active {
  transform: translateX(0);
}

/* Cart Header */
.cart-header {
  padding: 1.5rem;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--cream);
}

.cart-header h3 {
  margin: 0;
  color: var(--text-dark);
  font-size: 1.3rem;
  font-weight: 600;
}

.cart-close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-light);
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.cart-close-btn:hover {
  background-color: var(--primary-brown);
  color: white;
}

/* Cart Content */
.cart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Empty Cart */
.cart-empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.empty-cart-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.cart-empty p {
  color: var(--text-light);
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

/* Cart Items */
.cart-items {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.cart-item {
  display: flex;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid #eee;
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cart-item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.cart-item-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0;
  line-height: 1.3;
}

.cart-item-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-brown);
  margin: 0;
}

.cart-item-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

/* Quantity Controls */
.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 20px;
  padding: 0.25rem;
}

.quantity-btn {
  width: 30px;
  height: 30px;
  border: none;
  background-color: var(--primary-brown);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.quantity-btn:hover {
  background-color: var(--dark-brown);
  transform: scale(1.1);
}

.quantity {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
  color: var(--text-dark);
}

.remove-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  color: var(--text-light);
}

.remove-btn:hover {
  background-color: #fee;
  color: #e74c3c;
  transform: scale(1.1);
}

/* Cart Footer */
.cart-footer {
  padding: 1.5rem;
  border-top: 1px solid #eee;
  background-color: #f8f9fa;
}

.cart-total {
  margin-bottom: 1.5rem;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-dark);
}

.total-price {
  color: var(--primary-brown);
  font-size: 1.4rem;
  font-weight: 700;
}

.cart-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.clear-cart-btn {
  flex: 1;
  padding: 12px;
  border: 2px solid var(--text-light);
  background-color: transparent;
  color: var(--text-light);
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.clear-cart-btn:hover {
  background-color: var(--text-light);
  color: white;
}

.checkout-btn {
  flex: 2;
  padding: 12px;
  background-color: var(--primary-brown);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.checkout-btn:hover {
  background-color: var(--dark-brown);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
}

.continue-shopping-btn {
  width: 100%;
  padding: 12px;
  background-color: transparent;
  color: var(--primary-brown);
  border: 2px solid var(--primary-brown);
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.continue-shopping-btn:hover {
  background-color: var(--primary-brown);
  color: white;
}

/* Responsive Design */
@media (max-width: 480px) {
  .cart-sidebar {
    width: 100vw;
  }
  
  .cart-item {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .cart-item-image {
    width: 100%;
    height: 120px;
  }
  
  .cart-item-controls {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .quantity-controls {
    justify-content: center;
  }
  
  .cart-actions {
    flex-direction: column;
  }
}

/* Scrollbar Styling */
.cart-items::-webkit-scrollbar {
  width: 6px;
}

.cart-items::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.cart-items::-webkit-scrollbar-thumb {
  background: var(--primary-brown);
  border-radius: 3px;
}

.cart-items::-webkit-scrollbar-thumb:hover {
  background: var(--dark-brown);
}
