import React, { useState } from 'react';
import { useCart } from '../../context/CartContext';
import CheckoutModal from '../Checkout/CheckoutModal';
import './CartSidebar.css';

const CartSidebar = ({ isOpen, onClose }) => {
  const {
    items,
    totalItems,
    formattedTotalPrice,
    updateQuantity,
    removeFromCart,
    clearCart
  } = useCart();
  const [isCheckoutOpen, setIsCheckoutOpen] = useState(false);

  const handleQuantityChange = (id, newQuantity) => {
    if (newQuantity < 1) {
      removeFromCart(id);
    } else {
      updateQuantity(id, newQuantity);
    }
  };

  const formatPrice = (price) => {
    const numericPrice = parseFloat(price.replace(/[,\.]/g, ''));
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(numericPrice);
  };

  const handleCheckout = () => {
    setIsCheckoutOpen(true);
  };

  return (
    <>
      {/* Overlay */}
      <div
        className={`cart-overlay ${isOpen ? 'active' : ''}`}
        onClick={onClose}
      />

      {/* Sidebar */}
      <div className={`cart-sidebar ${isOpen ? 'active' : ''}`}>
        <div className="cart-header">
          <h3>Giỏ hàng ({totalItems})</h3>
          <button className="cart-close-btn" onClick={onClose}>
            ✕
          </button>
        </div>

        <div className="cart-content">
          {items.length === 0 ? (
            <div className="cart-empty">
              <div className="empty-cart-icon">🛒</div>
              <p>Giỏ hàng của bạn đang trống</p>
              <button className="continue-shopping-btn" onClick={onClose}>
                Tiếp tục mua sắm
              </button>
            </div>
          ) : (
            <>
              <div className="cart-items">
                {items.map(item => (
                  <div key={item.id} className="cart-item">
                    <div className="cart-item-image">
                      <img src={item.image} alt={item.name} />
                    </div>

                    <div className="cart-item-details">
                      <h4 className="cart-item-name">{item.name}</h4>
                      <p className="cart-item-price">{formatPrice(item.price)}</p>

                      <div className="cart-item-controls">
                        <div className="quantity-controls">
                          <button
                            className="quantity-btn"
                            onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          >
                            −
                          </button>
                          <span className="quantity">{item.quantity}</span>
                          <button
                            className="quantity-btn"
                            onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          >
                            +
                          </button>
                        </div>

                        <button
                          className="remove-btn"
                          onClick={() => removeFromCart(item.id)}
                          title="Xóa khỏi giỏ hàng"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="cart-footer">
                <div className="cart-total">
                  <div className="total-row">
                    <span>Tổng cộng:</span>
                    <span className="total-price">{formattedTotalPrice}</span>
                  </div>
                </div>

                <div className="cart-actions">
                  <button
                    className="clear-cart-btn"
                    onClick={clearCart}
                  >
                    Xóa tất cả
                  </button>
                  <button
                    className="checkout-btn"
                    onClick={handleCheckout}
                  >
                    Thanh toán
                  </button>
                </div>

                <button
                  className="continue-shopping-btn"
                  onClick={onClose}
                >
                  Tiếp tục mua sắm
                </button>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Checkout Modal */}
      <CheckoutModal
        isOpen={isCheckoutOpen}
        onClose={() => setIsCheckoutOpen(false)}
      />
    </>
  );
};

export default CartSidebar;
