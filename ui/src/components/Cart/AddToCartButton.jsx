import React, { useState } from 'react';
import { useCart } from '../../context/CartContext';
import { useNotification } from '../Notification/NotificationSystem';
import './AddToCartButton.css';

const AddToCartButton = ({
  product,
  className = '',
  children = 'Thêm vào giỏ',
  showQuantity = false,
  variant = 'primary' // 'primary', 'icon', 'small'
}) => {
  const { addToCart, isInCart, getItemQuantity, updateQuantity } = useCart();
  const { showCartNotification } = useNotification();
  const [isAdding, setIsAdding] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleAddToCart = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    setIsAdding(true);

    try {
      addToCart(product, 1);

      // Show success animation
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 1000);

      // Show notification
      showCartNotification(`Đã thêm "${product.name}" vào giỏ hàng!`);

    } catch (error) {
      console.error('Error adding to cart:', error);
      showCartNotification('Có lỗi xảy ra khi thêm sản phẩm vào giỏ hàng', 'Lỗi');
    } finally {
      setIsAdding(false);
    }
  };

  const handleQuantityChange = (newQuantity) => {
    if (newQuantity <= 0) {
      updateQuantity(product.id, 0);
    } else {
      updateQuantity(product.id, newQuantity);
    }
  };

  const currentQuantity = getItemQuantity(product.id);
  const productInCart = isInCart(product.id);

  // Icon variant (for product overlay)
  if (variant === 'icon') {
    return (
      <button
        className={`add-to-cart-icon ${className} ${isAdding ? 'adding' : ''} ${showSuccess ? 'success' : ''}`}
        onClick={handleAddToCart}
        disabled={isAdding}
        title="Thêm vào giỏ hàng"
      >
        {showSuccess ? '✓' : '🛒'}
      </button>
    );
  }

  // Small variant (for compact spaces)
  if (variant === 'small') {
    return (
      <button
        className={`add-to-cart-small ${className} ${isAdding ? 'adding' : ''} ${showSuccess ? 'success' : ''}`}
        onClick={handleAddToCart}
        disabled={isAdding}
      >
        {isAdding ? '...' : showSuccess ? '✓' : '+'}
      </button>
    );
  }

  // Primary variant (default button)
  return (
    <div className={`add-to-cart-container ${className}`}>
      {!productInCart || !showQuantity ? (
        <button
          className={`add-to-cart-btn ${isAdding ? 'adding' : ''} ${showSuccess ? 'success' : ''}`}
          onClick={handleAddToCart}
          disabled={isAdding}
        >
          <span className="btn-text">
            {isAdding ? 'Đang thêm...' : showSuccess ? 'Đã thêm!' : children}
          </span>
          <span className="btn-icon">
            {showSuccess ? '✓' : '🛒'}
          </span>
        </button>
      ) : (
        <div className="quantity-selector">
          <button
            className="quantity-btn decrease"
            onClick={() => handleQuantityChange(currentQuantity - 1)}
          >
            −
          </button>
          <span className="quantity-display">{currentQuantity}</span>
          <button
            className="quantity-btn increase"
            onClick={() => handleQuantityChange(currentQuantity + 1)}
          >
            +
          </button>
        </div>
      )}
    </div>
  );
};

export default AddToCartButton;
