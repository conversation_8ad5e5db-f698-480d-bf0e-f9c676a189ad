<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CoffeeSource - <PERSON><PERSON><PERSON><PERSON> li<PERSON>u cà phê chất lư<PERSON> cao</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-brown: #8B4513;
            --dark-brown: #5D2F0A;
            --light-brown: #D2B48C;
            --cream: #F5F5DC;
            --warm-white: #FFF8DC;
            --text-dark: #2C1810;
            --text-light: #6B4423;
            --accent-gold: #DAA520;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background-color: var(--warm-white);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Navbar */
        .navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 1rem 0;
        }

        .navbar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-logo h2 {
            color: var(--primary-brown);
            font-weight: 700;
            font-size: 1.8rem;
        }

        .navbar-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .navbar-menu a {
            color: var(--text-dark);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-menu a:hover {
            color: var(--primary-brown);
        }

        .navbar-icons {
            display: flex;
            gap: 1rem;
            font-size: 1.2rem;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            background-image: linear-gradient(135deg, rgba(44, 24, 16, 0.8) 0%, rgba(139, 69, 19, 0.6) 50%, rgba(44, 24, 16, 0.8) 100%),
                              url('https://images.unsplash.com/photo-1447933601403-0c6688de566e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2061&q=80');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
        }

        .hero-content {
            max-width: 800px;
            padding: 2rem;
            z-index: 2;
        }

        .hero-content h1 {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            line-height: 1.2;
            color: white;
        }

        .hero-content p {
            font-size: 1.5rem;
            margin-bottom: 2.5rem;
            color: var(--cream);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            line-height: 1.6;
        }

        .btn-primary {
            background-color: var(--accent-gold);
            color: var(--text-dark);
            padding: 15px 40px;
            border: none;
            border-radius: 25px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary:hover {
            background-color: #B8860B;
            transform: translateY(-3px);
        }

        /* Sections */
        .section {
            padding: 80px 0;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 50px;
            color: var(--text-dark);
        }

        /* Product Categories */
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .category-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .category-card:hover {
            transform: translateY(-5px);
        }

        .category-image {
            height: 250px;
            background-size: cover;
            background-position: center;
        }

        .category-content {
            padding: 1.5rem;
        }

        .category-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        /* Featured Products */
        .featured-products {
            background-color: #f8f8f8;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-image {
            height: 200px;
            background-size: cover;
            background-position: center;
        }

        .product-content {
            padding: 1.5rem;
        }

        .product-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .product-price {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--primary-brown);
            margin: 1rem 0;
        }

        /* Why Choose Us */
        .why-choose-us {
            background: linear-gradient(135deg, var(--dark-brown) 0%, var(--primary-brown) 100%);
            color: white;
        }

        .why-choose-us .section-title {
            color: white;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            text-align: center;
            padding: 2rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1.5rem;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        /* Footer */
        .footer {
            background: linear-gradient(135deg, var(--text-dark) 0%, var(--dark-brown) 100%);
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-title {
            color: var(--accent-gold);
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .copyright {
            text-align: center;
            color: var(--cream);
            border-top: 1px solid var(--primary-brown);
            padding-top: 1rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .navbar-menu {
                display: none;
            }

            .hero-content h1 {
                font-size: 2.5rem;
            }

            .categories-grid,
            .products-grid,
            .features-grid {
                grid-template-columns: 1fr;
            }

            .footer-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-content">
                <div class="navbar-logo">
                    <h2>CoffeeSource</h2>
                </div>
                <ul class="navbar-menu">
                    <li><a href="#home">Trang chủ</a></li>
                    <li><a href="#products">Sản phẩm</a></li>
                    <li><a href="#about">Về chúng tôi</a></li>
                    <li><a href="#contact">Liên hệ</a></li>
                </ul>
                <div class="navbar-icons">
                    <span>🔍</span>
                    <span>🛒</span>
                    <span>👤</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1>Nguyên liệu cà phê chất lượng cao</h1>
            <p>Cung cấp những hạt cà phê tốt nhất cho doanh nghiệp của bạn</p>
            <button class="btn-primary">Khám phá ngay</button>
        </div>
    </section>

    <!-- Product Categories -->
    <section class="section" id="categories">
        <div class="container">
            <h2 class="section-title">Danh mục sản phẩm</h2>
            <div class="categories-grid">
                <div class="category-card">
                    <div class="category-image" style="background-image: url('https://images.unsplash.com/photo-1559056199-641a0ac8b55e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')"></div>
                    <div class="category-content">
                        <h3 class="category-title">Cà phê nhân</h3>
                        <p>Hạt cà phê xanh chất lượng cao, nguồn gốc rõ ràng</p>
                    </div>
                </div>
                <div class="category-card">
                    <div class="category-image" style="background-image: url('https://images.unsplash.com/photo-1514432324607-a09d9b4aefdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')"></div>
                    <div class="category-content">
                        <h3 class="category-title">Cà phê rang</h3>
                        <p>Cà phê đã rang với nhiều mức độ khác nhau</p>
                    </div>
                </div>
                <div class="category-card">
                    <div class="category-image" style="background-image: url('https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')"></div>
                    <div class="category-content">
                        <h3 class="category-title">Thiết bị & Dụng cụ</h3>
                        <p>Máy móc và dụng cụ pha chế chuyên nghiệp</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products -->
    <section class="section featured-products" id="products">
        <div class="container">
            <h2 class="section-title">Sản phẩm nổi bật</h2>
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image" style="background-image: url('https://images.unsplash.com/photo-1559056199-641a0ac8b55e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')"></div>
                    <div class="product-content">
                        <h3 class="product-name">Arabica Đà Lạt</h3>
                        <p>Hạt cà phê Arabica cao cấp từ Đà Lạt</p>
                        <div class="product-price">250,000đ</div>
                        <button class="btn-primary">Thêm vào giỏ</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image" style="background-image: url('https://images.unsplash.com/photo-1514432324607-a09d9b4aefdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')"></div>
                    <div class="product-content">
                        <h3 class="product-name">Robusta Buôn Ma Thuột</h3>
                        <p>Cà phê Robusta đậm đà từ Buôn Ma Thuột</p>
                        <div class="product-price">180,000đ</div>
                        <button class="btn-primary">Thêm vào giỏ</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image" style="background-image: url('https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')"></div>
                    <div class="product-content">
                        <h3 class="product-name">Máy xay cà phê</h3>
                        <p>Máy xay cà phê chuyên nghiệp cho quán</p>
                        <div class="product-price">2,500,000đ</div>
                        <button class="btn-primary">Thêm vào giỏ</button>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image" style="background-image: url('https://images.unsplash.com/photo-1442512595331-e89e73853f31?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80')"></div>
                    <div class="product-content">
                        <h3 class="product-name">Bộ dụng cụ pha chế</h3>
                        <p>Bộ dụng cụ pha chế cà phê hoàn chỉnh</p>
                        <div class="product-price">850,000đ</div>
                        <button class="btn-primary">Thêm vào giỏ</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us -->
    <section class="section why-choose-us" id="about">
        <div class="container">
            <h2 class="section-title">Tại sao chọn chúng tôi?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <h3 class="feature-title">Chất lượng đảm bảo</h3>
                    <p>Sản phẩm được kiểm tra nghiêm ngặt, đảm bảo chất lượng cao nhất</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🚚</div>
                    <h3 class="feature-title">Giao hàng nhanh</h3>
                    <p>Giao hàng toàn quốc trong 24-48h, đảm bảo hàng hóa tươi ngon</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎧</div>
                    <h3 class="feature-title">Hỗ trợ 24/7</h3>
                    <p>Đội ngũ tư vấn chuyên nghiệp, sẵn sàng hỗ trợ mọi lúc</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🏆</div>
                    <h3 class="feature-title">Uy tín hàng đầu</h3>
                    <p>Hơn 10 năm kinh nghiệm, được tin tưởng bởi hàng nghìn khách hàng</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form -->
    <section class="section" id="contact">
        <div class="container">
            <h2 class="section-title">Liên hệ với chúng tôi</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; margin-top: 3rem;">
                <div>
                    <h3 style="font-size: 1.8rem; margin-bottom: 2rem;">Thông tin liên hệ</h3>
                    <div style="margin-bottom: 2rem;">
                        <h4 style="color: var(--primary-brown); margin-bottom: 0.5rem;">📍 Địa chỉ</h4>
                        <p>123 Nguyễn Văn Linh, Quận 7, TP.HCM</p>
                    </div>
                    <div style="margin-bottom: 2rem;">
                        <h4 style="color: var(--primary-brown); margin-bottom: 0.5rem;">📞 Điện thoại</h4>
                        <p>+84 123 456 789</p>
                    </div>
                    <div style="margin-bottom: 2rem;">
                        <h4 style="color: var(--primary-brown); margin-bottom: 0.5rem;">✉️ Email</h4>
                        <p><EMAIL></p>
                    </div>
                </div>
                <div style="background: white; padding: 2.5rem; border-radius: 15px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);">
                    <form>
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Họ và tên *</label>
                            <input type="text" style="width: 100%; padding: 12px 15px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;" placeholder="Nhập họ và tên của bạn">
                        </div>
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Email *</label>
                            <input type="email" style="width: 100%; padding: 12px 15px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;" placeholder="Nhập địa chỉ email">
                        </div>
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Tin nhắn *</label>
                            <textarea style="width: 100%; padding: 12px 15px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem; min-height: 120px; resize: vertical;" placeholder="Nhập tin nhắn của bạn"></textarea>
                        </div>
                        <button type="submit" class="btn-primary" style="width: 100%; padding: 15px;">Gửi tin nhắn</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div>
                    <h3 class="footer-title">CoffeeSource</h3>
                    <p style="color: var(--cream); line-height: 1.6; margin-bottom: 1.5rem;">
                        Chuyên cung cấp nguyên liệu cà phê chất lượng cao cho các doanh nghiệp.
                        Với hơn 10 năm kinh nghiệm, chúng tôi cam kết mang đến những sản phẩm tốt nhất.
                    </p>
                    <div style="display: flex; gap: 1rem;">
                        <span style="width: 40px; height: 40px; background-color: var(--primary-brown); display: flex; align-items: center; justify-content: center; border-radius: 50%; cursor: pointer;">📘</span>
                        <span style="width: 40px; height: 40px; background-color: var(--primary-brown); display: flex; align-items: center; justify-content: center; border-radius: 50%; cursor: pointer;">📷</span>
                        <span style="width: 40px; height: 40px; background-color: var(--primary-brown); display: flex; align-items: center; justify-content: center; border-radius: 50%; cursor: pointer;">🐦</span>
                    </div>
                </div>
                <div>
                    <h4 style="color: white; font-size: 1.3rem; margin-bottom: 1.5rem;">Liên hệ</h4>
                    <div style="color: var(--cream);">
                        <p style="margin-bottom: 1rem;">📍 123 Nguyễn Văn Linh, Quận 7, TP.HCM</p>
                        <p style="margin-bottom: 1rem;">📞 +84 123 456 789</p>
                        <p style="margin-bottom: 1rem;">✉️ <EMAIL></p>
                    </div>
                </div>
                <div>
                    <h4 style="color: white; font-size: 1.3rem; margin-bottom: 1.5rem;">Liên kết</h4>
                    <div style="color: var(--cream);">
                        <p style="margin-bottom: 0.8rem;"><a href="#home" style="color: var(--cream); text-decoration: none;">Trang chủ</a></p>
                        <p style="margin-bottom: 0.8rem;"><a href="#products" style="color: var(--cream); text-decoration: none;">Sản phẩm</a></p>
                        <p style="margin-bottom: 0.8rem;"><a href="#about" style="color: var(--cream); text-decoration: none;">Về chúng tôi</a></p>
                        <p style="margin-bottom: 0.8rem;"><a href="#contact" style="color: var(--cream); text-decoration: none;">Liên hệ</a></p>
                    </div>
                </div>
                <div>
                    <h4 style="color: white; font-size: 1.3rem; margin-bottom: 1.5rem;">Giờ làm việc</h4>
                    <div style="color: var(--cream);">
                        <p style="margin-bottom: 0.5rem;">Thứ 2 - Thứ 6: 8:00 - 18:00</p>
                        <p style="margin-bottom: 0.5rem;">Thứ 7: 8:00 - 12:00</p>
                        <p style="margin-bottom: 0.5rem;">Chủ nhật: Nghỉ</p>
                    </div>
                </div>
            </div>
            <div class="copyright">
                <p>© 2024 CoffeeSource. Tất cả quyền được bảo lưu.</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Simple form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Cảm ơn bạn đã liên hệ! Chúng tôi sẽ phản hồi sớm nhất.');
        });
    </script>
</body>
</html>
