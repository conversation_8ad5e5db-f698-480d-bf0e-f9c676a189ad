{"name": "coffeesource-api", "version": "1.0.0", "description": "CoffeeSource API Backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seedData.js", "test": "node scripts/testAPI.js"}, "keywords": ["coffee", "api", "nodejs", "express", "mongodb"], "author": "CoffeeSource Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "body-parser": "^1.20.2", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"nodemon": "^3.0.2", "axios": "^1.6.2"}}