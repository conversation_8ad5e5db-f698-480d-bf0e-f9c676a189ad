# CoffeeSource API

A RESTful API backend for the CoffeeSource coffee eCommerce platform built with Node.js, Express.js, and MongoDB.

## Features

- **Product Management**: Categories and products with full CRUD operations
- **Contact Form**: Handle customer inquiries and contact submissions
- **Database**: MongoDB with Mongoose ODM
- **Validation**: Comprehensive input validation and error handling
- **CORS**: Cross-origin resource sharing enabled for frontend integration
- **Environment Configuration**: Secure environment variable management

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose
- **Middleware**: CORS, body-parser, dotenv

## API Endpoints

### Public Endpoints

#### Categories
- `GET /api/categories` - Get all categories
- `GET /api/categories/:id` - Get single category

#### Products
- `GET /api/products` - Get all products (with filtering, pagination)
- `GET /api/products/featured` - Get featured products
- `GET /api/products/:id` - Get single product

#### Contact
- `POST /api/contact` - Submit contact form

#### Health Check
- `GET /api/health` - API health status
- `GET /` - API information

### Admin Endpoints (Authentication Required)

#### Authentication
- `POST /api/admin/auth/login` - Admin login
- `POST /api/admin/auth/register` - Register new admin (super_admin only)
- `GET /api/admin/auth/me` - Get current admin info
- `POST /api/admin/auth/logout` - Logout

#### Dashboard
- `GET /api/admin/dashboard` - Get dashboard overview
- `GET /api/admin/dashboard/analytics` - Get detailed analytics

#### Category Management
- `GET /api/admin/categories` - Get all categories (including inactive)
- `POST /api/admin/categories` - Create new category
- `PUT /api/admin/categories/:id` - Update category
- `DELETE /api/admin/categories/:id` - Delete category
- `GET /api/admin/categories/stats` - Get category statistics

#### Product Management
- `GET /api/admin/products` - Get all products (including inactive)
- `POST /api/admin/products` - Create new product
- `PUT /api/admin/products/:id` - Update product
- `DELETE /api/admin/products/:id` - Delete product
- `GET /api/admin/products/stats` - Get product statistics

#### Contact Management
- `GET /api/admin/contacts` - Get all contact submissions
- `GET /api/admin/contacts/:id` - Get single contact submission
- `PUT /api/admin/contacts/:id` - Update contact status
- `DELETE /api/admin/contacts/:id` - Delete contact submission
- `POST /api/admin/contacts/bulk-update` - Bulk update contacts
- `GET /api/admin/contacts/stats` - Get contact statistics

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd api
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```

   Update the `.env` file with your configuration:
   ```env
   PORT=5000
   MONGODB_URI=mongodb://localhost:27017/coffeesource
   NODE_ENV=development
   FRONTEND_URL=http://localhost:3000
   ```

4. **Start MongoDB**
   Make sure MongoDB is running on your system.

5. **Seed the database**
   ```bash
   npm run seed
   ```

6. **Start the server**
   ```bash
   # Development mode with auto-restart
   npm run dev

   # Production mode
   npm start
   ```

## Database Schema

### Category
```javascript
{
  name: String (required),
  imageUrl: String (required),
  description: String,
  isActive: Boolean (default: true),
  timestamps: true
}
```

### Product
```javascript
{
  name: String (required),
  description: String (required),
  price: Number (required),
  originalPrice: Number,
  category: ObjectId (ref: Category),
  imageUrl: String (required),
  badge: String (enum: ['Bán chạy', 'Giảm giá', 'Mới']),
  inStock: Boolean (default: true),
  stockQuantity: Number (default: 0),
  isFeatured: Boolean (default: false),
  isActive: Boolean (default: true),
  timestamps: true
}
```

### Contact
```javascript
{
  name: String (required),
  email: String (required),
  phone: String,
  message: String (required),
  status: String (enum: ['new', 'read', 'replied', 'resolved']),
  isRead: Boolean (default: false),
  adminNotes: String,
  timestamps: true
}
```

### Admin
```javascript
{
  username: String (required, unique),
  email: String (required, unique),
  password: String (required, hashed),
  role: String (enum: ['admin', 'super_admin']),
  isActive: Boolean (default: true),
  lastLogin: Date,
  loginAttempts: Number (default: 0),
  lockUntil: Date,
  timestamps: true
}
```

## API Usage Examples

### Get all categories
```bash
curl -X GET http://localhost:5000/api/categories
```

### Get featured products
```bash
curl -X GET http://localhost:5000/api/products/featured
```

### Submit contact form
```bash
curl -X POST http://localhost:5000/api/contact \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "0123456789",
    "message": "I am interested in your coffee products."
  }'
```

### Filter products
```bash
# Get products by category
curl -X GET "http://localhost:5000/api/products?category=CATEGORY_ID"

# Get featured products only
curl -X GET "http://localhost:5000/api/products?featured=true"

# Search products
curl -X GET "http://localhost:5000/api/products?search=arabica"

# Price range filter
curl -X GET "http://localhost:5000/api/products?minPrice=100000&maxPrice=500000"
```

## Error Handling

The API returns consistent error responses:

```javascript
{
  "status": "error",
  "message": "Error description",
  "errors": ["Detailed error messages"] // For validation errors
}
```

## Development

### Scripts
- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run seed` - Seed database with sample data

### Environment Variables
- `PORT` - Server port (default: 5000)
- `MONGODB_URI` - MongoDB connection string
- `NODE_ENV` - Environment (development/production)
- `FRONTEND_URL` - Frontend URL for CORS

## Production Deployment

1. Set environment variables for production
2. Ensure MongoDB is accessible
3. Run `npm start` to start the server
4. Consider using PM2 for process management

## License

MIT License
