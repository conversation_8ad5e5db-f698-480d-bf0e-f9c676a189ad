const express = require('express');
const router = express.Router();
const Contact = require('../../models/Contact');
const { authenticateAdmin } = require('../../middleware/auth');

// Apply authentication to all routes
router.use(authenticateAdmin);

// GET /api/admin/contacts - Get all contact submissions
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      sort = '-createdAt',
      search,
      status,
      isRead
    } = req.query;

    // Build filter
    const filter = {};
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { message: { $regex: search, $options: 'i' } }
      ];
    }

    if (status) filter.status = status;
    if (isRead !== undefined) filter.isRead = isRead === 'true';

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute query
    const contacts = await Contact.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(Number(limit));

    const total = await Contact.countDocuments(filter);

    res.status(200).json({
      status: 'success',
      results: contacts.length,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / limit)
      },
      data: {
        contacts
      }
    });
  } catch (error) {
    console.error('Error fetching contacts:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch contacts',
      error: error.message
    });
  }
});

// GET /api/admin/contacts/:id - Get single contact submission
router.get('/:id', async (req, res) => {
  try {
    const contact = await Contact.findById(req.params.id);

    if (!contact) {
      return res.status(404).json({
        status: 'error',
        message: 'Contact submission not found'
      });
    }

    // Mark as read when admin views it
    if (!contact.isRead) {
      contact.isRead = true;
      await contact.save();
    }

    res.status(200).json({
      status: 'success',
      data: {
        contact
      }
    });
  } catch (error) {
    console.error('Error fetching contact:', error);

    if (error.name === 'CastError') {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid contact ID'
      });
    }

    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch contact',
      error: error.message
    });
  }
});

// PUT /api/admin/contacts/:id - Update contact status
router.put('/:id', async (req, res) => {
  try {
    const { status, isRead, adminNotes } = req.body;

    const contact = await Contact.findByIdAndUpdate(
      req.params.id,
      { status, isRead, adminNotes },
      { new: true, runValidators: true }
    );

    if (!contact) {
      return res.status(404).json({
        status: 'error',
        message: 'Contact submission not found'
      });
    }

    res.status(200).json({
      status: 'success',
      message: 'Contact updated successfully',
      data: {
        contact
      }
    });
  } catch (error) {
    console.error('Error updating contact:', error);

    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        status: 'error',
        message: 'Validation error',
        errors
      });
    }

    if (error.name === 'CastError') {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid contact ID'
      });
    }

    res.status(500).json({
      status: 'error',
      message: 'Failed to update contact',
      error: error.message
    });
  }
});

// DELETE /api/admin/contacts/:id - Delete contact submission
router.delete('/:id', async (req, res) => {
  try {
    const contact = await Contact.findByIdAndDelete(req.params.id);

    if (!contact) {
      return res.status(404).json({
        status: 'error',
        message: 'Contact submission not found'
      });
    }

    res.status(200).json({
      status: 'success',
      message: 'Contact submission deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting contact:', error);

    if (error.name === 'CastError') {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid contact ID'
      });
    }

    res.status(500).json({
      status: 'error',
      message: 'Failed to delete contact',
      error: error.message
    });
  }
});

// POST /api/admin/contacts/bulk-update - Bulk update contact status
router.post('/bulk-update', async (req, res) => {
  try {
    const { contactIds, status, isRead } = req.body;

    if (!contactIds || !Array.isArray(contactIds) || contactIds.length === 0) {
      return res.status(400).json({
        status: 'error',
        message: 'Contact IDs array is required'
      });
    }

    const updateData = {};
    if (status) updateData.status = status;
    if (isRead !== undefined) updateData.isRead = isRead;

    const result = await Contact.updateMany(
      { _id: { $in: contactIds } },
      updateData
    );

    res.status(200).json({
      status: 'success',
      message: `Updated ${result.modifiedCount} contact submissions`,
      data: {
        modifiedCount: result.modifiedCount
      }
    });
  } catch (error) {
    console.error('Error bulk updating contacts:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to bulk update contacts',
      error: error.message
    });
  }
});

// GET /api/admin/contacts/stats - Get contact statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = await Contact.aggregate([
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          unread: { $sum: { $cond: ['$isRead', 0, 1] } },
          read: { $sum: { $cond: ['$isRead', 1, 0] } }
        }
      }
    ]);

    const statusStats = await Contact.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Recent contacts (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentStats = await Contact.aggregate([
      {
        $match: {
          createdAt: { $gte: sevenDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    res.status(200).json({
      status: 'success',
      data: {
        stats: stats[0] || { total: 0, unread: 0, read: 0 },
        statusStats,
        recentStats
      }
    });
  } catch (error) {
    console.error('Error fetching contact stats:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch contact statistics',
      error: error.message
    });
  }
});

module.exports = router;
