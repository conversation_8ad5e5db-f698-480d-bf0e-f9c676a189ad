const express = require('express');
const router = express.Router();
const Category = require('../../models/Category');
const Product = require('../../models/Product');
const Contact = require('../../models/Contact');
const Admin = require('../../models/Admin');
const { authenticateAdmin } = require('../../middleware/auth');

// Apply authentication to all routes
router.use(authenticateAdmin);

// GET /api/admin/dashboard - Get dashboard overview
router.get('/', async (req, res) => {
  try {
    // Get current date ranges
    const now = new Date();
    const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const startOfWeek = new Date(startOfToday);
    startOfWeek.setDate(startOfToday.getDate() - startOfToday.getDay());
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Categories statistics
    const categoryStats = await Category.aggregate([
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          active: { $sum: { $cond: ['$isActive', 1, 0] } }
        }
      }
    ]);

    // Products statistics
    const productStats = await Product.aggregate([
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          active: { $sum: { $cond: ['$isActive', 1, 0] } },
          featured: { $sum: { $cond: ['$isFeatured', 1, 0] } },
          inStock: { $sum: { $cond: ['$inStock', 1, 0] } },
          totalValue: { $sum: { $multiply: ['$price', '$stockQuantity'] } },
          totalStock: { $sum: '$stockQuantity' }
        }
      }
    ]);

    // Contact statistics
    const contactStats = await Contact.aggregate([
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          unread: { $sum: { $cond: ['$isRead', 0, 1] } },
          today: {
            $sum: {
              $cond: [
                { $gte: ['$createdAt', startOfToday] },
                1,
                0
              ]
            }
          },
          thisWeek: {
            $sum: {
              $cond: [
                { $gte: ['$createdAt', startOfWeek] },
                1,
                0
              ]
            }
          },
          thisMonth: {
            $sum: {
              $cond: [
                { $gte: ['$createdAt', startOfMonth] },
                1,
                0
              ]
            }
          }
        }
      }
    ]);

    // Recent contacts (last 5)
    const recentContacts = await Contact.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('name email message createdAt status isRead');

    // Low stock products (stock < 10)
    const lowStockProducts = await Product.find({
      isActive: true,
      inStock: true,
      stockQuantity: { $lt: 10 }
    })
      .populate('category', 'name')
      .select('name stockQuantity category')
      .sort({ stockQuantity: 1 })
      .limit(10);

    // Recent products (last 5)
    const recentProducts = await Product.find({ isActive: true })
      .populate('category', 'name')
      .sort({ createdAt: -1 })
      .limit(5)
      .select('name price category createdAt isFeatured');

    // Contact trends (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const contactTrends = await Contact.aggregate([
      {
        $match: {
          createdAt: { $gte: sevenDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Top categories by product count
    const topCategories = await Product.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: '$category',
          productCount: { $sum: 1 },
          totalValue: { $sum: { $multiply: ['$price', '$stockQuantity'] } }
        }
      },
      {
        $lookup: {
          from: 'categories',
          localField: '_id',
          foreignField: '_id',
          as: 'category'
        }
      },
      { $unwind: '$category' },
      {
        $project: {
          name: '$category.name',
          productCount: 1,
          totalValue: 1
        }
      },
      { $sort: { productCount: -1 } },
      { $limit: 5 }
    ]);

    // Admin activity (for super_admin)
    let adminStats = null;
    if (req.admin.role === 'super_admin') {
      adminStats = await Admin.aggregate([
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            active: { $sum: { $cond: ['$isActive', 1, 0] } },
            locked: {
              $sum: {
                $cond: [
                  { $gt: ['$lockUntil', new Date()] },
                  1,
                  0
                ]
              }
            }
          }
        }
      ]);
    }

    res.status(200).json({
      status: 'success',
      data: {
        overview: {
          categories: categoryStats[0] || { total: 0, active: 0 },
          products: productStats[0] || { 
            total: 0, 
            active: 0, 
            featured: 0, 
            inStock: 0, 
            totalValue: 0, 
            totalStock: 0 
          },
          contacts: contactStats[0] || { 
            total: 0, 
            unread: 0, 
            today: 0, 
            thisWeek: 0, 
            thisMonth: 0 
          },
          admins: adminStats ? adminStats[0] : null
        },
        recentContacts,
        lowStockProducts,
        recentProducts,
        contactTrends,
        topCategories
      }
    });

  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch dashboard data',
      error: error.message
    });
  }
});

// GET /api/admin/dashboard/analytics - Get detailed analytics
router.get('/analytics', async (req, res) => {
  try {
    const { period = '30' } = req.query; // days
    const daysAgo = new Date();
    daysAgo.setDate(daysAgo.getDate() - parseInt(period));

    // Contact analytics
    const contactAnalytics = await Contact.aggregate([
      {
        $match: {
          createdAt: { $gte: daysAgo }
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
            status: '$status'
          },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: '$_id.date',
          statuses: {
            $push: {
              status: '$_id.status',
              count: '$count'
            }
          },
          total: { $sum: '$count' }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Product performance
    const productPerformance = await Product.aggregate([
      { $match: { isActive: true } },
      {
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'category'
        }
      },
      { $unwind: '$category' },
      {
        $group: {
          _id: '$category.name',
          productCount: { $sum: 1 },
          averagePrice: { $avg: '$price' },
          totalValue: { $sum: { $multiply: ['$price', '$stockQuantity'] } },
          featuredCount: { $sum: { $cond: ['$isFeatured', 1, 0] } }
        }
      },
      { $sort: { totalValue: -1 } }
    ]);

    res.status(200).json({
      status: 'success',
      data: {
        contactAnalytics,
        productPerformance,
        period: parseInt(period)
      }
    });

  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch analytics',
      error: error.message
    });
  }
});

module.exports = router;
