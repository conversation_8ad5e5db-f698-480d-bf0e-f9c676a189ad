const express = require('express');
const router = express.Router();
const Admin = require('../../models/Admin');
const { generateToken, authenticateAdmin, checkLoginRateLimit } = require('../../middleware/auth');

// POST /api/admin/auth/login - Admin login
router.post('/login', checkLoginRateLimit, async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'Username and password are required'
      });
    }

    // Find admin by username or email
    const admin = await Admin.findOne({
      $or: [
        { username: username.toLowerCase() },
        { email: username.toLowerCase() }
      ]
    });

    if (!admin) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid credentials'
      });
    }

    // Check if account is locked
    if (admin.isLocked) {
      return res.status(401).json({
        status: 'error',
        message: 'Account is temporarily locked due to failed login attempts'
      });
    }

    // Check if account is active
    if (!admin.isActive) {
      return res.status(401).json({
        status: 'error',
        message: 'Account is deactivated'
      });
    }

    // Verify password
    const isPasswordValid = await admin.comparePassword(password);

    if (!isPasswordValid) {
      await admin.incLoginAttempts();
      return res.status(401).json({
        status: 'error',
        message: 'Invalid credentials'
      });
    }

    // Reset login attempts on successful login
    await admin.resetLoginAttempts();

    // Generate token
    const token = generateToken(admin._id);

    res.status(200).json({
      status: 'success',
      message: 'Login successful',
      data: {
        admin: {
          id: admin._id,
          username: admin.username,
          email: admin.email,
          role: admin.role
        },
        token
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Login failed',
      error: error.message
    });
  }
});

// POST /api/admin/auth/register - Register new admin (super_admin only)
router.post('/register', authenticateAdmin, async (req, res) => {
  try {
    // Only super_admin can create new admins
    if (req.admin.role !== 'super_admin') {
      return res.status(403).json({
        status: 'error',
        message: 'Only super administrators can create new admin accounts'
      });
    }

    const { username, email, password, role = 'admin' } = req.body;

    // Validate required fields
    if (!username || !email || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'Username, email, and password are required'
      });
    }

    // Check if admin already exists
    const existingAdmin = await Admin.findOne({
      $or: [
        { username: username.toLowerCase() },
        { email: email.toLowerCase() }
      ]
    });

    if (existingAdmin) {
      return res.status(400).json({
        status: 'error',
        message: 'Admin with this username or email already exists'
      });
    }

    // Create new admin
    const admin = new Admin({
      username: username.toLowerCase(),
      email: email.toLowerCase(),
      password,
      role
    });

    await admin.save();

    res.status(201).json({
      status: 'success',
      message: 'Admin account created successfully',
      data: {
        admin: {
          id: admin._id,
          username: admin.username,
          email: admin.email,
          role: admin.role
        }
      }
    });

  } catch (error) {
    console.error('Registration error:', error);

    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        status: 'error',
        message: 'Validation error',
        errors
      });
    }

    res.status(500).json({
      status: 'error',
      message: 'Registration failed',
      error: error.message
    });
  }
});

// GET /api/admin/auth/me - Get current admin info
router.get('/me', authenticateAdmin, async (req, res) => {
  try {
    res.status(200).json({
      status: 'success',
      data: {
        admin: req.admin
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Failed to get admin info',
      error: error.message
    });
  }
});

// POST /api/admin/auth/logout - Logout (client-side token removal)
router.post('/logout', authenticateAdmin, async (req, res) => {
  try {
    res.status(200).json({
      status: 'success',
      message: 'Logout successful'
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Logout failed',
      error: error.message
    });
  }
});

module.exports = router;
