const express = require('express');
const router = express.Router();
const Product = require('../../models/Product');
const { authenticateAdmin } = require('../../middleware/auth');

// Apply authentication to all routes
router.use(authenticateAdmin);

// GET /api/admin/products - Get all products (including inactive)
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      sort = '-createdAt',
      search,
      category,
      isActive,
      isFeatured,
      inStock
    } = req.query;

    // Build filter
    const filter = {};
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    if (category) filter.category = category;
    if (isActive !== undefined) filter.isActive = isActive === 'true';
    if (isFeatured !== undefined) filter.isFeatured = isFeatured === 'true';
    if (inStock !== undefined) filter.inStock = inStock === 'true';

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute query
    const products = await Product.find(filter)
      .populate('category', 'name imageUrl')
      .sort(sort)
      .skip(skip)
      .limit(Number(limit));

    const total = await Product.countDocuments(filter);

    res.status(200).json({
      status: 'success',
      results: products.length,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / limit)
      },
      data: {
        products
      }
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch products',
      error: error.message
    });
  }
});

// POST /api/admin/products - Create new product
router.post('/', async (req, res) => {
  try {
    const {
      name,
      description,
      price,
      originalPrice,
      category,
      imageUrl,
      badge,
      isFeatured,
      inStock,
      stockQuantity
    } = req.body;

    const product = new Product({
      name,
      description,
      price,
      originalPrice,
      category,
      imageUrl,
      badge,
      isFeatured,
      inStock,
      stockQuantity
    });

    await product.save();
    await product.populate('category', 'name imageUrl');

    res.status(201).json({
      status: 'success',
      message: 'Product created successfully',
      data: {
        product
      }
    });
  } catch (error) {
    console.error('Error creating product:', error);

    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        status: 'error',
        message: 'Validation error',
        errors
      });
    }

    res.status(500).json({
      status: 'error',
      message: 'Failed to create product',
      error: error.message
    });
  }
});

// PUT /api/admin/products/:id - Update product
router.put('/:id', async (req, res) => {
  try {
    const {
      name,
      description,
      price,
      originalPrice,
      category,
      imageUrl,
      badge,
      isFeatured,
      inStock,
      stockQuantity,
      isActive
    } = req.body;

    const product = await Product.findByIdAndUpdate(
      req.params.id,
      {
        name,
        description,
        price,
        originalPrice,
        category,
        imageUrl,
        badge,
        isFeatured,
        inStock,
        stockQuantity,
        isActive
      },
      { new: true, runValidators: true }
    ).populate('category', 'name imageUrl');

    if (!product) {
      return res.status(404).json({
        status: 'error',
        message: 'Product not found'
      });
    }

    res.status(200).json({
      status: 'success',
      message: 'Product updated successfully',
      data: {
        product
      }
    });
  } catch (error) {
    console.error('Error updating product:', error);

    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        status: 'error',
        message: 'Validation error',
        errors
      });
    }

    if (error.name === 'CastError') {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid product ID'
      });
    }

    res.status(500).json({
      status: 'error',
      message: 'Failed to update product',
      error: error.message
    });
  }
});

// DELETE /api/admin/products/:id - Delete product
router.delete('/:id', async (req, res) => {
  try {
    const product = await Product.findByIdAndDelete(req.params.id);

    if (!product) {
      return res.status(404).json({
        status: 'error',
        message: 'Product not found'
      });
    }

    res.status(200).json({
      status: 'success',
      message: 'Product deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting product:', error);

    if (error.name === 'CastError') {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid product ID'
      });
    }

    res.status(500).json({
      status: 'error',
      message: 'Failed to delete product',
      error: error.message
    });
  }
});

// GET /api/admin/products/stats - Get product statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = await Product.aggregate([
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          active: { $sum: { $cond: ['$isActive', 1, 0] } },
          featured: { $sum: { $cond: ['$isFeatured', 1, 0] } },
          inStock: { $sum: { $cond: ['$inStock', 1, 0] } },
          outOfStock: { $sum: { $cond: ['$inStock', 0, 1] } },
          totalValue: { $sum: { $multiply: ['$price', '$stockQuantity'] } }
        }
      }
    ]);

    const categoryStats = await Product.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          totalValue: { $sum: { $multiply: ['$price', '$stockQuantity'] } }
        }
      },
      {
        $lookup: {
          from: 'categories',
          localField: '_id',
          foreignField: '_id',
          as: 'category'
        }
      },
      {
        $unwind: '$category'
      },
      {
        $project: {
          categoryName: '$category.name',
          count: 1,
          totalValue: 1
        }
      }
    ]);

    res.status(200).json({
      status: 'success',
      data: {
        stats: stats[0] || { 
          total: 0, 
          active: 0, 
          featured: 0, 
          inStock: 0, 
          outOfStock: 0, 
          totalValue: 0 
        },
        categoryStats
      }
    });
  } catch (error) {
    console.error('Error fetching product stats:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch product statistics',
      error: error.message
    });
  }
});

module.exports = router;
