const express = require('express');
const router = express.Router();
const Category = require('../../models/Category');
const { authenticateAdmin } = require('../../middleware/auth');

// Apply authentication to all routes
router.use(authenticateAdmin);

// GET /api/admin/categories - Get all categories (including inactive)
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      sort = '-createdAt',
      search,
      isActive
    } = req.query;

    // Build filter
    const filter = {};
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    if (isActive !== undefined) {
      filter.isActive = isActive === 'true';
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute query
    const categories = await Category.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(Number(limit));

    const total = await Category.countDocuments(filter);

    res.status(200).json({
      status: 'success',
      results: categories.length,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / limit)
      },
      data: {
        categories
      }
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch categories',
      error: error.message
    });
  }
});

// POST /api/admin/categories - Create new category
router.post('/', async (req, res) => {
  try {
    const { name, imageUrl, description } = req.body;

    // Check if category already exists
    const existingCategory = await Category.findOne({
      name: { $regex: new RegExp(`^${name}$`, 'i') }
    });

    if (existingCategory) {
      return res.status(400).json({
        status: 'error',
        message: 'Category with this name already exists'
      });
    }

    const category = new Category({
      name,
      imageUrl,
      description
    });

    await category.save();

    res.status(201).json({
      status: 'success',
      message: 'Category created successfully',
      data: {
        category
      }
    });
  } catch (error) {
    console.error('Error creating category:', error);

    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        status: 'error',
        message: 'Validation error',
        errors
      });
    }

    res.status(500).json({
      status: 'error',
      message: 'Failed to create category',
      error: error.message
    });
  }
});

// PUT /api/admin/categories/:id - Update category
router.put('/:id', async (req, res) => {
  try {
    const { name, imageUrl, description, isActive } = req.body;

    // Check if another category with same name exists
    if (name) {
      const existingCategory = await Category.findOne({
        name: { $regex: new RegExp(`^${name}$`, 'i') },
        _id: { $ne: req.params.id }
      });

      if (existingCategory) {
        return res.status(400).json({
          status: 'error',
          message: 'Another category with this name already exists'
        });
      }
    }

    const category = await Category.findByIdAndUpdate(
      req.params.id,
      { name, imageUrl, description, isActive },
      { new: true, runValidators: true }
    );

    if (!category) {
      return res.status(404).json({
        status: 'error',
        message: 'Category not found'
      });
    }

    res.status(200).json({
      status: 'success',
      message: 'Category updated successfully',
      data: {
        category
      }
    });
  } catch (error) {
    console.error('Error updating category:', error);

    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        status: 'error',
        message: 'Validation error',
        errors
      });
    }

    if (error.name === 'CastError') {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid category ID'
      });
    }

    res.status(500).json({
      status: 'error',
      message: 'Failed to update category',
      error: error.message
    });
  }
});

// DELETE /api/admin/categories/:id - Delete category
router.delete('/:id', async (req, res) => {
  try {
    const category = await Category.findByIdAndDelete(req.params.id);

    if (!category) {
      return res.status(404).json({
        status: 'error',
        message: 'Category not found'
      });
    }

    res.status(200).json({
      status: 'success',
      message: 'Category deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting category:', error);

    if (error.name === 'CastError') {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid category ID'
      });
    }

    res.status(500).json({
      status: 'error',
      message: 'Failed to delete category',
      error: error.message
    });
  }
});

// GET /api/admin/categories/stats - Get category statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = await Category.aggregate([
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          active: { $sum: { $cond: ['$isActive', 1, 0] } },
          inactive: { $sum: { $cond: ['$isActive', 0, 1] } }
        }
      }
    ]);

    res.status(200).json({
      status: 'success',
      data: {
        stats: stats[0] || { total: 0, active: 0, inactive: 0 }
      }
    });
  } catch (error) {
    console.error('Error fetching category stats:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch category statistics',
      error: error.message
    });
  }
});

module.exports = router;
