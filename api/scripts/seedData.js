const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Category = require('../models/Category');
const Product = require('../models/Product');
const Contact = require('../models/Contact');
const Admin = require('../models/Admin');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/coffeesource', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Seed data
const seedCategories = [
  {
    name: 'Cà phê nhân',
    imageUrl: 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    description: '<PERSON><PERSON><PERSON> cà phê xanh chấ<PERSON> l<PERSON> cao, nguồn gốc rõ ràng từ các vùng trồng nổi tiếng'
  },
  {
    name: 'Cà phê rang',
    imageUrl: 'https://images.unsplash.com/photo-1514432324607-a09d9b4aefdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    description: 'Cà phê đã rang với nhiều mức độ khác nhau, từ rang nhẹ đến rang đậm'
  },
  {
    name: 'Thiết bị & Dụng cụ',
    imageUrl: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    description: 'Máy móc và dụng cụ pha chế cà phê chuyên nghiệp cho quán cà phê'
  }
];

const seedProducts = [
  {
    name: 'Arabica Đà Lạt',
    description: 'Hạt cà phê Arabica cao cấp từ Đà Lạt với hương vị thơm ngon, độ chua nhẹ và vị ngọt tự nhiên',
    price: 250000,
    originalPrice: 300000,
    imageUrl: 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    badge: 'Bán chạy',
    isFeatured: true,
    inStock: true,
    stockQuantity: 100
  },
  {
    name: 'Robusta Buôn Ma Thuột',
    description: 'Cà phê Robusta đậm đà từ Buôn Ma Thuột với hương vị mạnh mẽ, đắng nhẹ và caffeine cao',
    price: 180000,
    imageUrl: 'https://images.unsplash.com/photo-1514432324607-a09d9b4aefdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    isFeatured: true,
    inStock: true,
    stockQuantity: 150
  },
  {
    name: 'Máy xay cà phê chuyên nghiệp',
    description: 'Máy xay cà phê chuyên nghiệp cho quán với công suất lớn, xay đều và bền bỉ',
    price: 2500000,
    originalPrice: 3000000,
    imageUrl: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    badge: 'Giảm giá',
    isFeatured: true,
    inStock: true,
    stockQuantity: 20
  },
  {
    name: 'Bộ dụng cụ pha chế hoàn chỉnh',
    description: 'Bộ dụng cụ pha chế cà phê hoàn chỉnh bao gồm phin, bình pha, cân điện tử và các phụ kiện',
    price: 850000,
    imageUrl: 'https://images.unsplash.com/photo-1442512595331-e89e73853f31?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    badge: 'Mới',
    isFeatured: true,
    inStock: true,
    stockQuantity: 50
  },
  {
    name: 'Cà phê Moka Cầu Đất',
    description: 'Cà phê Moka từ Cầu Đất với hương vị độc đáo, cân bằng giữa vị chua và ngọt',
    price: 320000,
    imageUrl: 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    isFeatured: false,
    inStock: true,
    stockQuantity: 80
  },
  {
    name: 'Cà phê rang vừa Specialty',
    description: 'Cà phê rang vừa từ hạt Specialty grade với quy trình rang thủ công, giữ nguyên hương vị tự nhiên',
    price: 420000,
    imageUrl: 'https://images.unsplash.com/photo-1514432324607-a09d9b4aefdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    isFeatured: false,
    inStock: true,
    stockQuantity: 60
  }
];

const seedContacts = [
  {
    name: 'Nguyễn Văn An',
    email: '<EMAIL>',
    phone: '0123456789',
    message: 'Tôi muốn tìm hiểu về các sản phẩm cà phê Arabica của quý công ty. Xin vui lòng liên hệ lại.',
    status: 'new'
  },
  {
    name: 'Trần Thị Bình',
    email: '<EMAIL>',
    phone: '0987654321',
    message: 'Quán cà phê của tôi cần đặt hàng số lượng lớn. Có chính sách giá sỉ không?',
    status: 'read'
  }
];

const seedAdmins = [
  {
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'super_admin'
  },
  {
    username: 'manager',
    email: '<EMAIL>',
    password: 'manager123',
    role: 'admin'
  }
];

// Seed function
const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    // Clear existing data
    await Category.deleteMany({});
    await Product.deleteMany({});
    await Contact.deleteMany({});
    await Admin.deleteMany({});
    console.log('🗑️  Cleared existing data');

    // Seed categories
    const categories = await Category.insertMany(seedCategories);
    console.log(`✅ Seeded ${categories.length} categories`);

    // Update products with category IDs
    const updatedProducts = seedProducts.map((product, index) => {
      if (index < 2) {
        // First 2 products are coffee beans
        product.category = categories[0]._id; // Cà phê nhân
      } else if (index < 4) {
        // Next 2 products are equipment
        product.category = categories[2]._id; // Thiết bị & Dụng cụ
      } else {
        // Remaining products are roasted coffee
        product.category = categories[1]._id; // Cà phê rang
      }
      return product;
    });

    // Seed products
    const products = await Product.insertMany(updatedProducts);
    console.log(`✅ Seeded ${products.length} products`);

    // Seed contacts
    const contacts = await Contact.insertMany(seedContacts);
    console.log(`✅ Seeded ${contacts.length} contact submissions`);

    // Seed admin users
    const admins = await Admin.insertMany(seedAdmins);
    console.log(`✅ Seeded ${admins.length} admin users`);

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   Categories: ${categories.length}`);
    console.log(`   Products: ${products.length}`);
    console.log(`   Contacts: ${contacts.length}`);
    console.log(`   Admins: ${admins.length}`);
    console.log('\n👤 Admin Credentials:');
    console.log('   Super Admin - Username: admin, Password: admin123');
    console.log('   Admin - Username: manager, Password: manager123');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
};

// Run seeding
connectDB().then(() => {
  seedDatabase();
});
